import 'package:bhagawathgitaapp/data/data.dart';
import 'package:bhagawathgitaapp/model/data_objects.dart';
import 'package:bhagawathgitaapp/model/sharedPreferences/favorite.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:bhagawathgitaapp/screens/services/navigator_helper.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/audio_screen.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/custom_sloka_page.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/list_view_screen.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/youtube_screen.dart';
import 'package:bhagawathgitaapp/screens/widgets/favorite_button.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:provider/provider.dart';

Future<List<ListTileInfo>> impSlokaFuture(
    BuildContext context, String title, Function refresh) async {
  final adService = Provider.of<NavigationAdService>(context, listen: false);

  List<ListTileInfo> objs = [];
  for (var chp_slk in impSlokaData[title]["data"]) {
    var chp_slk_arr = chp_slk.split(":");
    int chapterNo = int.parse(chp_slk_arr[0]);
    int slokaNo = int.parse(chp_slk_arr[1]);
    objs.add(ListTileInfo(
        icon: const Icon(
          Icons.auto_stories,
          color: Colors.red,
        ),
        text: Text(
          "$chapterNoవ అధ్యాయము $slokaNoవ శ్లోకం",
          style: GoogleFonts.peddana(color: Colors.blue, fontSize: 20),
        ),
        action: () {
          NavigationHelper.navigateWithAd(
                  context,
                  PageRouteBuilder(
                      pageBuilder: (context, a1, a2) => CustomSlokaPage(
                            title: title,
                            sloka: chp_slk,
                            slokas: impSlokaData[title]["data"],
                          )),
                  adService)
              .then((value) {
            refresh();
          });
        },
        endIcon: (impSlokaData[title]["FavButton"])
            ? FavoriteButton(chapterNo: chapterNo, slokaNo: slokaNo)
            : null));
  }
  return objs;
}

Future favSlokaFuture(context, Function refresh) async {
  final adService = Provider.of<NavigationAdService>(context, listen: false);
  List<ListTileInfo> objs = [];
  List<String> slokas = await getAllFavorites();
  for (var i in slokas) {
    var l = i.split(":");
    int chapterNo = int.parse(l[0]);
    int slokaNo = int.parse(l[1]);
    objs.add(ListTileInfo(
      icon: const Icon(
        Icons.brightness_high_outlined,
        color: Colors.red,
      ),
      text: Text(
        "$chapterNoవ అధ్యాయము $slokaNoవ శ్లోకం",
        style: GoogleFonts.peddana(color: Colors.blue, fontSize: 20),
      ),
      action: () {
        NavigationHelper.navigateWithAd(
                context,
                PageRouteBuilder(
                    pageBuilder: (context, a1, a2) => CustomSlokaPage(
                          title: "ఇష్టమైన శ్లోకాలు",
                          sloka: i,
                          slokas: slokas,
                        )),
                adService)
            .then((value) {
          refresh();
        });
      },
      //endIcon: FavoriteButton(chapterNo: chapterNo,slokaNo:slokaNo),
    ));
  }
  return objs;
}

List<SongInfo> parseSongs(var data) {
  List<SongInfo> songs = [];
  for (var i in data["Objects"]) {
    if (i["type"] == "song") {
      songs.add(SongInfo(name: i["name"], url: i["url"]));
    }
  }
  return songs;
}

Future getSongs(context, String url, Function refresh) async {
  final response = await http.get(Uri.parse(url));
  var data = json.decode(response.body);
  List<SongInfo> songs = parseSongs(data);
  List<ListTileInfo> objs = [];

  final adService = Provider.of<NavigationAdService>(context, listen: false);

  int no = 0;
  for (var i in data["Objects"]) {
    int index = no;
    Icon customIcon;
    Text text;
    Function() action;

    if (i["type"] == "song") {
      customIcon = const Icon(Icons.music_note, color: Colors.green);
      text = Text(
        i["name"],
        style: GoogleFonts.mallanna(color: Colors.black, fontSize: 20),
      );
      action = () {
        NavigationHelper.navigateWithAd(
          context,
          PageRouteBuilder(
              pageBuilder: (context, a1, a2) =>
                  AudioScreen(songs: songs, index: index)),
          adService,
        );
      };
      no++;
    } else {
      customIcon = const Icon(Icons.folder, color: Colors.orange);
      text = Text(
        i["name"],
        style: GoogleFonts.mallanna(color: Colors.black, fontSize: 20),
      );
      action = () {
        NavigationHelper.navigateWithAd(
            context,
            PageRouteBuilder(
                pageBuilder: (context, a1, a2) => ListViewScreen(
                      title: "శ్రీకృష్ణుని గేయాలు",
                      futureData: getSongs,
                      id: i["url"],
                    )),
            adService);
      };
    }
    objs.add(ListTileInfo(icon: customIcon, text: text, action: action));
  }
  return objs;
}

Future getVideos(context, String url, Function refresh) async {
  final response = await http.get(Uri.parse(url));
  var data = json.decode(response.body);
  List<ListTileInfo> objs = [];
  for (var i in data["Objects"]) {
    Icon customIcon;
    Text text;
    Function() action;

    if (i["type"] == "video") {
      customIcon = const Icon(Icons.video_collection, color: Colors.green);
      text = Text(
        i["name"],
        style: GoogleFonts.mallanna(color: Colors.black, fontSize: 20),
      );
      action = () {
        Navigator.push(
            context,
            PageRouteBuilder(
                pageBuilder: (context, a1, a2) =>
                    CustomYotubePlayer(youtubeID: i["id"])));
      };
    } else {
      customIcon = const Icon(Icons.folder, color: Colors.orange);
      text = Text(
        i["name"],
        style: GoogleFonts.mallanna(color: Colors.black, fontSize: 20),
      );
      action = () {
        Navigator.push(
            context,
            PageRouteBuilder(
                pageBuilder: (context, a1, a2) => ListViewScreen(
                      title: "భగవద్గిత వీడియోస్",
                      futureData: getVideos,
                      id: i["id"],
                    )));
      };
    }
    objs.add(ListTileInfo(icon: customIcon, text: text, action: action));
  }
  return objs;
}
