import 'package:bhagawathgitaapp/model/sharedPreferences/favorite.dart';
import 'package:flutter/material.dart';

class FavoriteButton extends StatefulWidget {
  final int chapterNo,slokaNo;
  const FavoriteButton({ 
    Key? key,
    required this.chapterNo,
    required this.slokaNo
     }) : super(key: key);

  @override
  _FavoriteButtonState createState() => _FavoriteButtonState();
}

class _FavoriteButtonState extends State<FavoriteButton> {
  bool isFav=false;
  bool fav=false;

  Future checkFavorite() async{
    fav= await isFavorite(widget.chapterNo, widget.slokaNo);
    if (fav!=isFav){
    setState((){
      isFav=fav;
    });}
  }

  @override
  Widget build(BuildContext context) {
    checkFavorite();
    return IconButton(
      onPressed: (){
        addFavorite(widget.chapterNo,widget.slokaNo);
        setState(() {
            isFav=!isFav;
        });}, 
      icon: Icon(isFav?Icons.favorite:Icons.favorite_border),
      color: isFav?Colors.red:Colors.black,
      );
  }
}