import 'package:bhagawathgitaapp/data/commentary_data.dart';
import 'package:bhagawathgitaapp/data/data.dart';
import 'package:bhagawathgitaapp/model/utils.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:bhagawathgitaapp/screens/services/navigator_helper.dart';
import 'package:bhagawathgitaapp/screens/widgets/audio_player.dart';
import 'package:bhagawathgitaapp/screens/widgets/gita_guru_bot.dart';
import 'package:flutter/material.dart';
import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:provider/provider.dart';

class SlokaContainer extends StatelessWidget {
  final Function() popupAction;
  final int chapterNo;
  final int slokaNo;
  final bool autoPlaySloka;
  final Function(bool) onSlokaAudioCompleted;
  const SlokaContainer({
    Key? key,
    required this.chapterNo,
    required this.slokaNo,
    required this.popupAction,
    required this.onSlokaAudioCompleted,
    required this.autoPlaySloka,
  }) : super(key: key);

  Container headingArea() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.all(20),
      child: Text(
        data["$chapterNo"]["$slokaNo"][0],
        style: GoogleFonts.mallanna(
          color: Colors.red,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Container slokaArea() {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.only(left: 20, right: 20),
      child: Text(
        data["$chapterNo"]["$slokaNo"][1],
        style: GoogleFonts.mallanna(
          color: Colors.blue,
          fontSize: 22,
          height: 1.3,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: SingleChildScrollView(
        child: Column(
          children: [
            headingArea(),
            const SizedBox(height: 10),
            slokaArea(),
            const SizedBox(height: 50),
            AudioplayerWidget(
              key: Key('$chapterNo $slokaNo'),
              url:
                  "https://akuladatta.github.io/gitaAudio/$chapterNo/$slokaNo.mp3",
              onCompleteForSmallWidget: onSlokaAudioCompleted,
              autoPlay: autoPlaySloka,
            ),
            const SizedBox(height: 30),
            MeaningBar(
              key: Key('Meaningbar_${chapterNo}_$slokaNo'),
              chapterNo: "$chapterNo",
              slokaNo: "$slokaNo",
              popupAction: popupAction,
            ),
          ],
        ),
      ),
    );
  }
}

class MeaningBar extends StatefulWidget {
  final String chapterNo, slokaNo;
  final Function() popupAction;
  const MeaningBar({
    required this.chapterNo,
    required this.slokaNo,
    required this.popupAction,
    Key? key,
  }) : super(key: key);

  @override
  _MeaningBarState createState() => _MeaningBarState();
}

class _MeaningBarState extends State<MeaningBar> {
  int meaningIndex = 0;
  late FlutterTts flutterTts;
  late List<String> headingList, meaningList;
  bool isPlaying = false;

  @override
  void initState() {
    flutterTts = FlutterTts();
    super.initState();
    _setTTSLanguage();

    flutterTts.setCompletionHandler(() {
      setState(() {
        isPlaying = false;
      });
    });
  }

  @override
  void dispose() {
    flutterTts.stop();
    super.dispose();
  }

  Future<void> _setTTSLanguage() async {
    await flutterTts.setLanguage("te-IN"); // తెలుగు భాష
    await flutterTts.setSpeechRate(0.55); // మాట్లాడే వేగం
    await flutterTts.setPitch(1.2); // పిచ్
  }

  Future<void> _speak(String text) async {
    await flutterTts.speak(text);
  }

  BoxDecoration decoration() {
    return const BoxDecoration(
      borderRadius: BorderRadius.all(Radius.circular(35)),
      color: Colors.white,
      boxShadow: [
        BoxShadow(offset: Offset(0, 8), blurRadius: 14, color: Colors.grey),
      ],
    );
  }

  toggleButton() {
    if (meaningList[1] == "") {
      setState(() {
        meaningIndex = 0;
      });
      return Container(height: 30);
    }
    return Center(
      child: FlutterToggleTab(
        width: 60,
        borderRadius: 30,
        height: 50,
        selectedIndex: meaningIndex,
        selectedBackgroundColors: [Colors.yellow[400]!],
        selectedTextStyle: GoogleFonts.peddana(
          color: Colors.black,
          fontSize: 20,
        ),
        unSelectedTextStyle: GoogleFonts.mallanna(
          color: Colors.black87,
          fontSize: 17,
          fontWeight: FontWeight.w500,
        ),
        // labels: ["భావం", "వివరము"],
        dataTabs: [
          DataTab(title: "భావం"), // Example DataTab object
          DataTab(title: "వివరము"), // Example DataTab object
        ],
        selectedLabelIndex: (index) {
          if (meaningIndex != index) {
            setState(() {
              meaningIndex = index;
            });
          }
        },
      ),
    );
  }

  Future<void> _stop() async {
    await flutterTts.stop(); // Stops the audio
  }

  Column meaning() {
    final rewardAdService = Provider.of<RewardAdService>(
      context,
      listen: false,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(height: 15),
        toggleButton(),
        Container(height: 35),
        Row(
          children: [
            Text(
              headingList[meaningIndex],
              style: GoogleFonts.mallanna(
                color: Colors.red,
                fontSize: 21,
                height: 1.3,
                fontWeight: FontWeight.w400,
              ),
            ),
            IconButton(
              icon: Icon(
                isPlaying ? Icons.volume_off : Icons.volume_up,
                color: Colors.blue,
              ),
              onPressed: () {
                if (isPlaying) {
                  _stop();
                } else {
                  _speak(meaningList[meaningIndex]);
                }
                setState(() {
                  isPlaying = !isPlaying;
                });
              },
            ),
          ],
        ),
        Container(height: 10),
        Text(
          meaningList[meaningIndex],
          style: GoogleFonts.mallanna(
            color: Colors.black,
            fontSize: 22,
            height: 1.3,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 50),
        ElevatedButton.icon(
          icon: Icon(Icons.message_sharp, color: Colors.blueAccent),
          // style: ElevatedButton.styleFrom(
          //                 side: BorderSide(
          //     color: Colors.blue,
          //   ),

          // ),
          onPressed: () {
            RewardedNavigationHelper.navigateWithRewardedAd(
              // loadScreenBefore: false,
              showSuccessButton: true,
              context: context,
              route: MaterialPageRoute(
                builder:
                    (context) => GitaGuruChat(
                      // chapterNo: widget.chapterNo,
                      // slokaNo: widget.slokaNo,
                      initialMessage:
                          "ఈ శ్లోకంపై మరింత వివరము తెలపండి : \n  భగవద్గీత ${widget.chapterNo}వ అధ్యాయం ${widget.slokaNo}వ శ్లోకం \n\n" +
                          data[widget.chapterNo][widget.slokaNo][1] +
                          "\n\n" +
                          data[widget.chapterNo][widget.slokaNo][2],
                    ),
              ),
              adService: rewardAdService,
            );
            // Navigator.push(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) => GitaGuruChat(
            //       // chapterNo: widget.chapterNo,
            //       // slokaNo: widget.slokaNo,
            //       initialMessage: "ఈ శ్లోకంపై మరింత వివరము తెలపండి : \n\n  భగవద్గీత ${widget.chapterNo}వ అధ్యాయం ${widget.slokaNo}వ శ్లోకం \n\n"+ data[widget.chapterNo][widget.slokaNo][1] + "\n\n" + data[widget.chapterNo][widget.slokaNo][2],
            //     ),
            //   ),
            // );
          },
          label: Padding(
            padding: const EdgeInsets.symmetric(vertical: 1.0),
            child: Text(
              "ఈ శ్లోకం గురించి ఆధ్యాత్మిక గురువు ని అడిగి మరింత వివరము తెలుసుకోండి 🙏",
              textAlign: TextAlign.center,
              style: GoogleFonts.peddana(
                color: Colors.blueAccent,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        SizedBox(height: 50),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    headingList = [
      "భావం:",
      "వివరము: (" + commentaryData[widget.chapterNo][widget.slokaNo][0] + ")",
    ];
    meaningList = [
      data[widget.chapterNo][widget.slokaNo][2],
      commentaryData[widget.chapterNo][widget.slokaNo][1],
    ];
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 0),
          child: Container(
            padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
            alignment: Alignment.center,
            width: MediaQuery.of(context).size.width,
            decoration: decoration(),
            child: meaning(),
          ),
        ),
        Positioned(
          child: FloatingActionButton(
            heroTag: "share",
            backgroundColor: Colors.yellow,
            child: const Icon(Icons.share, color: Colors.green),
            onPressed: () {
              String text =
                  '''${widget.chapterNo}వ అధ్యాయము ${widget.slokaNo}వ శ్లోకం\n\n${data[widget.chapterNo][widget.slokaNo][1]}\n\nభావం:  ${data[widget.chapterNo][widget.slokaNo][2]}''';
              shareIt(text);
            },
          ),
          top: 0,
          right: 10,
        ),
        Positioned(
          child: FloatingActionButton(
            heroTag: "sloka-index",
            backgroundColor: Colors.yellow,
            child: Text(
              "${widget.chapterNo}:${widget.slokaNo}",
              style: const TextStyle(color: Colors.green),
            ),
            onPressed: widget.popupAction,
          ),
          top: 0,
          left: 10,
        ),
      ],
    );
  }
}
