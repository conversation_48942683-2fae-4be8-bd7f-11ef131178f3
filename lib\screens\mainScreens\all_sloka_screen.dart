
import 'package:bhagawathgitaapp/model/sloka_rotation.dart';
import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
// import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/constraints.dart';
import 'package:bhagawathgitaapp/screens/widgets/sloka_appbar.dart';
import 'package:bhagawathgitaapp/screens/widgets/sloka_container.dart';
import 'package:bhagawathgitaapp/screens/widgets/sloka_all_popup.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swipe_to/swipe_to.dart';

class AllSlokaScreen extends StatefulWidget {
  const AllSlokaScreen({ Key? key }) : super(key: key);
  @override
  _AllSlokaScreenState createState() => _AllSlokaScreenState();
}

class _AllSlokaScreenState extends State<AllSlokaScreen> {
  int chapterNo=1;
  int slokaNo=1;
  int screenChangeCount=0;
  bool autoPlay = false;

  @override
  void initState(){
    super.initState();
    Future.delayed(Duration.zero, () =>popupDialog(context,"$chapterNo","$slokaNo",popupUpdate));
  }

  @override
  Widget build(BuildContext context) {
    screenChangeCount++;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: SlokaAppbar(chapterNo: chapterNo,slokaNo: slokaNo,
                title: "$chapterNoవ అధ్యాయము $slokaNoవ శ్లోకం",
                leftAction: (){leftSwipe();},rightAction: (){rightSwipe();},),
      body: SwipeTo(
        // left and right swipes here are pointed towards the view of the app.
        onRightSwipe: (DragUpdateDetails dragUpdateDetails) {leftSwipe();},
        onLeftSwipe: (DragUpdateDetails dragUpdateDetails) {rightSwipe();},
        child: SlokaContainer(chapterNo:chapterNo,slokaNo:slokaNo,
        onSlokaAudioCompleted: (updateAutoPlay){
          setState(() {
            autoPlay=updateAutoPlay;
          });
          rightSwipe();},
        autoPlaySloka: autoPlay,
        popupAction: (){popupDialog(context,"$chapterNo","$slokaNo",popupUpdate);},)),
      // skipping the first time as it distrubes popup
      //bottomNavigationBar: (screenChangeCount!=1)?const TpBannerAd(bannerUnitId):Container(height: 0,),
      bottomNavigationBar: BannerAdWidget(location: "banner_sloka_container",) ,
    );
  }

  void popupUpdate(String chapter,String sloka){
    setState(() {
      chapterNo=int.parse(chapter);
      slokaNo=int.parse(sloka);
    });
  }

  void leftSwipe() async {
    
    final adService = Provider.of<NavigationAdService>(context, listen: false);
    await adService.handleContentUpdate();
    
    var l=leftRotate(chapterNo,slokaNo);
    setState(() {
      chapterNo=l[0];
      slokaNo=l[1];
    });}
  
  
  void rightSwipe() async{
    
    final adService = Provider.of<NavigationAdService>(context, listen: false);
    await adService.handleContentUpdate();

    var l=rightRotate(chapterNo,slokaNo);
    setState(() {
      chapterNo=l[0];
      slokaNo=l[1];
    });}

}