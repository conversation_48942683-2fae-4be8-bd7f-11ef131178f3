import 'package:bhagawathgitaapp/screens/mainScreens/all_sloka_screen.dart';
import 'package:bhagawathgitaapp/screens/mainScreens/imp_sloka_screen.dart';
import 'package:bhagawathgitaapp/screens/services/navigator_helper.dart';
import 'package:flutter/material.dart';

void changeScreen(context,screen,adService) {
    if (screen=="AllSloka"){
       NavigationHelper.navigateWithAd(
                          context,
                          PageRouteBuilder(
              pageBuilder: (context, a1, a2) => const AllSlokaScreen()),
                          adService,
                        );
              }
    else if (screen=="ImpSloka"){
      NavigationHelper.navigateWithAd(
                          context,
                          PageRouteBuilder(
              pageBuilder: (context, a1, a2) => const ImpSlokaScreen()),
                          adService,
                        );
      }
              
}