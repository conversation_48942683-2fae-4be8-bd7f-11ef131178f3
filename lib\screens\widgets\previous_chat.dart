import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flyer_chat_text_message/flyer_chat_text_message.dart';
import 'package:share_plus/share_plus.dart';

class ChatSession {
  final String id;
  final String title;
  final List<Message> messages;
  final int createdAt; // Timestamp in milliseconds

  ChatSession({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
  });
}

class PreviousChatsScreen extends StatelessWidget {
  final String currentChatId;
  const PreviousChatsScreen({required this.currentChatId, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Previous Chats 📝',
          style: GoogleFonts.openSans(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        elevation: 4,
      ),
      body: FutureBuilder<List<ChatSession>>(
        future: _loadChatSessions(),
        builder: (context, snapshot) {
          // Handle different states of the FutureBuilder
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error loading chats',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                ),
              ),
            );
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(
              child: Text(
                'No chats found',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
              ),
            );
          } else {
            final chatSessions = snapshot.data!;
            return ListView.separated(
              itemCount: chatSessions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: Colors.grey[300],
              ),
              itemBuilder: (context, index) {
                final chatSession = chatSessions[index];
                return ListTile(
                  title: Text(
                    chatSession.title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  subtitle: Text(
                    _formatDate(chatSession.createdAt),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            ChatPreviewScreen(chatSession: chatSession),
                      ),
                    );
                  },
                  trailing: const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey,
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }

// Helper function to format the date
  String _formatDate(int millisecondsSinceEpoch) {
    final date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
    final hour = date.hour;
    final amOrPm = hour < 12 ? 'AM' : 'PM';
    final hour12 = hour > 12
        ? hour - 12
        : hour == 0
            ? 12
            : hour;
    return '${date.day}-${date.month}-${date.year} $hour12:${date.minute.toString().padLeft(2, '0')} $amOrPm';
  }

  Future<List<ChatSession>> _loadChatSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final chatSessions = <ChatSession>[];
    final keys =
        prefs.getKeys().where((key) => key.startsWith('p2chat_')).toList();

    for (final key in keys) {
      final chatId = key.replaceFirst('p2chat_', '');
      if (chatId == currentChatId) {
        continue;
      }
      final chatData = prefs.getString(key);
      final chatTitle = prefs.getString('chat_title_$chatId');
      final chatTime = prefs.getInt('chat_time_$chatId');

      if (chatData != null && chatTitle != null && chatTime != null) {
        final messages = (jsonDecode(chatData) as List)
            .map((message) => Message.fromJson(message))
            .toList();
        chatSessions.add(ChatSession(
          id: chatId,
          title: chatTitle,
          messages: messages,
          createdAt: chatTime,
        ));
      }
    }

    // Sort chat sessions by creation time (newest first)
    chatSessions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return chatSessions;
  }
}

class ChatPreviewScreen extends StatelessWidget {
  final ChatSession chatSession;

  const ChatPreviewScreen({Key? key, required this.chatSession})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Convert stored messages to the new format
    final chatController = InMemoryChatController();
    
    // Add messages to controller
    for (final message in chatSession.messages) {
      chatController.insertMessage(message);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(chatSession.title),
      ),
      body: Chat(
        chatController: chatController,
        currentUserId: 'user',
        onMessageSend: (_) {}, // Disable sending new messages
        theme: ChatTheme.light(
          fontFamily: GoogleFonts.mallanna().fontFamily,
        ).copyWith(
          colors: ChatColors(
            primary: Colors.grey[200]!,
            onPrimary: Colors.black,
            surface: Colors.white,
            onSurface: Colors.black,
            surfaceContainer: Colors.deepPurple[100]!,
            surfaceContainerLow: Colors.deepPurple[50]!,
            surfaceContainerHigh: Colors.deepPurple[200]!,
          ),
          typography: ChatTypography.standard(
            fontFamily: GoogleFonts.mallanna().fontFamily,
          ).copyWith(
            bodyLarge: GoogleFonts.mallanna(
              fontSize: 18,
              color: Colors.black,
            ), // User sent
            bodyMedium: GoogleFonts.mallanna(
              fontSize: 18,
              color: Colors.black,
            ), // Received
          ),
          shape: BorderRadius.circular(12),
        ),
        resolveUser: (id) async {
          if (id == 'user') return User(id: id, name: 'User');
          if (id == 'saint') return User(id: id, name: 'ఆధ్యాత్మిక గురువు');
          if (id == 'system') return User(id: id, name: 'System');
          return User(id: id, name: 'Unknown');
        },
        builders: Builders(
          composerBuilder: (context) => const SizedBox.shrink(),
          textMessageBuilder: (context, message, index) => Row(
            children: [
              (message.authorId != 'user')
                  ? const SizedBox.shrink()
                  : SizedBox(width: 60),
              Expanded(
                child: FlyerChatTextMessage(
                  message: message,
                  index: index,
                ),
              ),
              (message.authorId == 'user')
                  ? const SizedBox.shrink()
                  : IconButton(
                      onPressed: () {
                        String text = message.text;
                        String appLink =
                            'https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu&hl=en';
                        String shareText = '''
${text}

-----------------------
భగవద్గీత తెలుగు యాప్ నుండి
👉 $appLink
''';
                        Share.share(
                          shareText,
                          subject: 'భగవద్గీత తెలుగు యాప్',
                        );
                      },
                      icon: Icon(Icons.share),
                    ),
            ],
          ),
          chatAnimatedListBuilder: (context, itemBuilder) {
                return ChatAnimatedList(
                  itemBuilder: itemBuilder,
                  scrollController: ScrollController(),
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.manual,
                  // Reduce scrolling sensitivity by using a custom ScrollController with slower scroll speed
                  // We can't directly set physics, but we can control other aspects
                  scrollToEndAnimationDuration: const Duration(milliseconds: 500),
                  initialScrollToEndMode: InitialScrollToEndMode.none,
                  // Increase animation duration to make scrolling feel less sensitive
                  insertAnimationDuration: const Duration(milliseconds: 400),
                  removeAnimationDuration: const Duration(milliseconds: 400),
                );
              },
          systemMessageBuilder: (context, systemMessage, locale) {
            return Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 8,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              child: Text(
                systemMessage.text,
                style: GoogleFonts.mallanna(
                  color: Colors.grey[600],
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            );
          },
        ),
      ),
    );
  }
}
