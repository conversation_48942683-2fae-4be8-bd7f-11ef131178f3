import 'package:bhagawathgitaapp/model/future_functions.dart';
import 'package:bhagawathgitaapp/model/global_router.dart';
import 'package:bhagawathgitaapp/model/sharedPreferences/custom_options.dart';
import 'package:bhagawathgitaapp/model/sharedPreferences/favorite.dart';
import 'package:bhagawathgitaapp/model/utils.dart';
import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/screens/ads/interstital_ads.dart';
import 'package:bhagawathgitaapp/screens/mainScreens/imp_sloka_screen.dart';
import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:bhagawathgitaapp/screens/services/force_update_service.dart';
import 'package:bhagawathgitaapp/screens/services/navigator_helper.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/list_view_screen.dart';
import 'package:bhagawathgitaapp/screens/widgets/additional_options.dart';
import 'package:bhagawathgitaapp/screens/widgets/datta_fullbutton.dart';
import 'package:bhagawathgitaapp/screens/widgets/datta_halfbutton.dart';
import 'package:bhagawathgitaapp/screens/widgets/datta_appbar.dart';
import 'package:bhagawathgitaapp/screens/widgets/datta_floatingtext.dart';
import 'package:bhagawathgitaapp/screens/widgets/gita_guru_bot.dart';
import 'package:bhagawathgitaapp/screens/widgets/menu.dart';
import 'package:bhagawathgitaapp/screens/widgets/today_quote.dart';
import 'package:flutter/material.dart';
// import 'package:bhagawathgitaapp/constraints.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:marquee/marquee.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String marqueeMessage = '';
  bool hasFavSlokas = false;
  final remoteConfig = RemoteConfigService();

  @override
  void initState() {
    super.initState();
    marqueeMessage = remoteConfig.getValue<String>(key: 'welcome_message');
    _checkAndShowQuoteDialog();
    getAllFavorites().then((value) => setState(() {
          hasFavSlokas = value.isNotEmpty;
        }));
  }

  Future<void> _checkAndShowQuoteDialog() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String todayDate = DateFormat('yyyy-MM-dd').format(DateTime.now());

    String? lastShownDate = prefs.getString('quote_last_shown_date');

    // Check if the dialog has already been shown today
    if (lastShownDate != todayDate) {
      // Show the BottomSheetDialog
      _showDailyQuoteDialog();

      // Save today's date as the last shown date
      await prefs.setString('quote_last_shown_date', todayDate);
    }
  }

  void _showDailyQuoteDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => TodayQuoteWidget(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final adService = Provider.of<NavigationAdService>(context, listen: false);
    final rewardAdService =
        Provider.of<RewardAdService>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ForceUpdateService.checkForUpdate(context);
    });

    // FIXME : Make this dynamic
    // if (MediaQuery.of(context).size.width > 700) {
    //   return webUI(context, adService);
    // } 
    return mobileUI(context, adService, rewardAdService);
  }

  Widget marquee(context) {
    return (marqueeMessage.trim() == '')
        ? SizedBox(
            height: 0,
          )
        : Container(
            padding: EdgeInsets.only(top: 1, bottom: 1),
            child: SizedBox(
              height: 25,
              child: Marquee(
                text: marqueeMessage,
                style: GoogleFonts.mallanna(
                  fontWeight: FontWeight.w500,
                  fontSize: 19,
                  height: 1.3,
                ),
                scrollAxis: Axis.horizontal,
                crossAxisAlignment: CrossAxisAlignment.start,
                blankSpace: 20.0,
                velocity: 50,
                pauseAfterRound: const Duration(seconds: 1),
                showFadingOnlyWhenScrolling: true,
                fadingEdgeStartFraction: 0.1,
                fadingEdgeEndFraction: 0.1,
                startPadding: 10.0,
                accelerationDuration: const Duration(seconds: 1),
                accelerationCurve: Curves.linear,
                decelerationDuration: const Duration(milliseconds: 500),
                decelerationCurve: Curves.easeOut,
              ),
            ),
          );
  }

  Scaffold mobileUI(BuildContext context, NavigationAdService adService,
      RewardAdService rewardAdService) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 80,
        elevation: 10,
        leading: Builder(
          builder: (BuildContext context) {
            return IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () => Scaffold.of(context).openDrawer(),
            );
          },
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 20,
            ),
            Text(
              "భగవద్గీత",
              style: GoogleFonts.dhurjati(color: Colors.black),
            ),
            Text(
              "మీ ఆధ్యాత్మిక మార్గదర్శి",
              style: GoogleFonts.mallanna(color: Colors.black, fontSize: 16),
            ),
            SizedBox(
              height: 20,
            ),
          ],
        ),
        actions: [
          (hasFavSlokas) ?
            IconButton(
              icon: Icon(Icons.favorite, color: Colors.red),
              onPressed: () {
                NavigationHelper.navigateWithAd(
                  context,
                  PageRouteBuilder(
                      pageBuilder: (context, a1, a2) => const ListViewScreen(
                          title: "మీకు ఇష్టమైన శ్లోకాలు",
                          futureData: favSlokaFuture)),
                  adService,
                );
              },
            ) :
          IconButton(
              icon: Icon(
                Icons.share,
                color: Colors.red,
              ),
              onPressed: () {
                String appLink =
                    'https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu&hl=en';
                String shareText = '''
📖 భగవద్గీత తెలుగు (App)
================================

ఈ యాప్‌లో మీరు 
- అన్ని అధ్యాయాలలోని శ్లోకాలను సులభంగా చదవచ్చు📜
- భావోద్వేగాల ప్రకారం భగవద్గీత శ్లోకాలు 😌
- భగవద్గీత ఆడియోలు 🎧
- పంచాంగం 🗓️
- మరియు భగవద్గీత ప్రకారం మీ సందేహాలకు సమాధానం చెప్పే AI గురువు 🤖 
ఇలా ఎన్నో విశేషాలు ఇందులో ఉన్నాయి.  

📲 డౌన్లోడ్ చేసుకోడానికి ఈ లింక్ నొక్కండి:
👉 $appLink
  ''';

                Share.share(shareText, subject: 'భగవద్గీత తెలుగు యాప్');
              })
        ],
      ),
      drawer: Menu(),
      body: Column(
        children: [
          Flexible(
            child: ListView(
              children: [
                const SizedBox(height: 10),
                // const DattaAppBarAsset(
                //   imagePath: "assets/main_krishna.png",
                //   text: "కృష్ణమ్ వందే\n జగద్గురం",
                //   right: -20,
                // ),
                // const DattaFloatingText(futureData: getMainFloatingText),

                marquee(context),
                DattaFullButtonAsset(
                  isReversed: true,
                  onTap: () {
                    changeScreen(context, "AllSloka", adService);
                  },
                  height: 170,
                  imagePath: "assets/main_krishna.png",
                  title: "సంపూర్ణ భగవద్గీత",
                  text: "అధ్యాయములు మరియు శ్లోకాల వారీగా సంపూర్ణ భగవత్గిత",
                ),
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  DattaHalfButtonAsset(
                    imagePath: "assets/krishna.png",
                    title: "ముఖ్యమైన శ్లోకాలు",
                    text: "కోపం, శాంతి, కామం మరియు అనేక భావోద్వేగాలపై శ్లోకాలు",
                    onTap: () {
                      //TpInterstitialAd.load(interstitialUnitAd, onLoadFailed: (){});
                      NavigationHelper.navigateWithAd(
                        context,
                        PageRouteBuilder(
                            pageBuilder: (context, a1, a2) =>
                                const ImpSlokaScreen()),
                        adService,
                      );
                      // Navigator.push(
                      //     context,
                      //     PageRouteBuilder(
                      //         pageBuilder: (context, a1, a2) =>
                      //             const ImpSlokaScreen()));
                      //.then((value){TpInterstitialAd.show(onDismissAd: (){});TpInterstitialAd.dispose();});
                    },
                  ),
                  const SizedBox(width: 20),
                  DattaHalfButtonAsset(
                    imagePath: "assets/krishna_flute.png",
                    title: "ఆడియోలు",
                    text:
                        "అధ్యాయాల వారీగా, సంపూర్ణ గీత మరియు శ్రీకృష్ణ భక్తి గీతాలు",
                    onTap: () {
                      // TpInterstitialAd.load(interstitialUnitAd,
                      //     onLoadFailed: () {});
                      NavigationHelper.navigateWithAd(
                        context,
                        PageRouteBuilder(
                            pageBuilder: (context, a1, a2) =>
                                const ListViewScreen(
                                  title: "భగవద్గిత  గేయాలు",
                                  futureData: getSongs,
                                  id: "https://airbushack-default-rtdb.firebaseio.com/gitaAudio/.json",
                                  haveIntersitialAd: true,
                                )),
                        adService,
                      );
                    },
                  )
                ]),
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  DattaHalfButtonAsset(
                    imagePath: "assets/saint.png",
                    title: "ఆధ్యాత్మిక గురువు తో మాట్లాడండి",
                    text:
                        "AI సహాయంతో  మీ బాధలు, సందేహాలు, మార్గసూచన తెలుసుకోండి ",
                    onTap: () {
                      RewardedNavigationHelper.navigateWithRewardedAd(
                        showSuccessButton: false,
                        context: context,
                        route: PageRouteBuilder(
                            pageBuilder: (context, a1, a2) => GitaGuruChat()),
                        adService: rewardAdService,
                      );
                    },
                  ),
                  const SizedBox(width: 20),
                  // DattaHalfButtonAsset(
                  //   imagePath: "assets/krishna_radha2.png",
                  //   title: "వీడియోలు",
                  //   text: "వీడియో రూపమ్ లో భగవత్గిత",
                  //   onTap: () {
                  //     // TpInterstitialAd.load(interstitialUnitAd, onLoadFailed: (){});
                  //     //Navigator.push(context,PageRouteBuilder(pageBuilder: (context, a1, a2) => const ListViewScreen(title:"భగవద్గిత వీడియోస్",futureData:getVideos,id: "https://airbushack-default-rtdb.firebaseio.com/gitaVideo/.json",haveIntersitialAd: true,)));
                  //     Navigator.push(
                  //         context,
                  //         PageRouteBuilder(
                  //             pageBuilder: (context, a1, a2) =>
                  //                 const ListViewScreen(
                  //                   title: "భగవద్గిత వీడియోస్",
                  //                   futureData: getVideos,
                  //                   id: "https://airbushack-default-rtdb.firebaseio.com/gitaVideo/.json",
                  //                   haveIntersitialAd: true,
                  //                 )));
                  //   },
                  // )
                  DattaHalfButtonAsset(
                      // imagePath: "assets/join_telegram.png",
                      hideImageSpace: true,
                      title: "మరిన్ని ఆప్షన్లు 🔽",
                      text:
                          "ఇష్టమైన స్లోకాలు, టెలిగ్రామ్ గ్రూప్‌, మంచి మాట మరిఎన్నో",
                      onTap: () {
                        showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            builder: (context) {
                              return AdditionalOptions();
                            });
                      })
                ]),
                SizedBox(
                  height: 30,
                )
              ],
            ),
          ),
        ],
      ),
      //bottomNavigationBar: const TpBannerAd(bannerUnitId),
      bottomNavigationBar: BannerAdWidget(
        location: "banner_homePage",
      ),
    );
  }

  Scaffold webUI(BuildContext context, NavigationAdService adService) {
    return Scaffold(
      body: Column(
        children: [
          Flexible(
            child: ListView(
              children: [
                const DattaAppBarAsset(
                  imagePath: "assets/main_krishna.png",
                  text: "కృష్ణమ్ వందే\n జగద్గురం",
                  right: 0,
                ),
                const DattaFloatingText(futureData: getMainFloatingText),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    DattaFullButtonAsset(
                      onTap: () {
                        changeScreen(context, "AllSloka", adService);
                      },
                      height: 230,
                      width: 2 * getScreenWidth(context) / 3 - 50,
                      imagePath: "assets/saint.png", // FIXME this is removed
                      title: "సంపూర్ణ భగవద్గీత",
                      text: "అధ్యాయములు మరియు శ్లోకాల వారీగా సంపూర్ణ భగవత్గిత",
                    ),
                    DattaHalfButtonAsset(
                      imagePath: "assets/krishna.png",
                      title: "ముఖ్యమైన శ్లోకాలు",
                      text:
                          "కోపం, శాంతి, కామం మరియు అనేక భావోద్వేగాలపై శ్లోకాలు",
                      width: getScreenWidth(context) / 2,
                      onTap: () {
                        //TpInterstitialAd.load(interstitialUnitAd, onLoadFailed: (){});
                        Navigator.push(
                            context,
                            PageRouteBuilder(
                                pageBuilder: (context, a1, a2) =>
                                    const ImpSlokaScreen()));
                        //.then((value){TpInterstitialAd.show(onDismissAd: (){});TpInterstitialAd.dispose();});
                      },
                    )
                  ],
                ),
                Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [const SizedBox(width: 20)]),
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  DattaHalfButtonAsset(
                    imagePath: "assets/saint.png", // FIXME this is removed
                    title: "ఇష్టమైన శ్లోకాలు",
                    text: "మీకు ఇష్టమైనవిగా గుర్తించబడిన పద్యాలు",
                    width: getScreenWidth(context) / 2 + 50,
                    onTap: () {
                      Navigator.push(
                          context,
                          PageRouteBuilder(
                              pageBuilder: (context, a1, a2) =>
                                  const ListViewScreen(
                                      title: "ఇష్టమైన శ్లోకాలు",
                                      futureData: favSlokaFuture)));
                    },
                  ),
                  const SizedBox(width: 30),
                  DattaHalfButtonAsset(
                    imagePath: "assets/krishna_flute.png",
                    title: "ఆడియోలు",
                    text: "ఆడియో రూపంలో భగవత్గీత",
                    width: getScreenWidth(context) / 2 + 50,
                    onTap: () {
                      // TpInterstitialAd.load(interstitialUnitAd,
                      //     onLoadFailed: () {});
                      Navigator.push(
                          context,
                          PageRouteBuilder(
                              pageBuilder: (context, a1, a2) =>
                                  const ListViewScreen(
                                    title: "భగవద్గిత  గేయాలు",
                                    futureData: getSongs,
                                    id: "https://airbushack-default-rtdb.firebaseio.com/gitaAudio/.json",
                                    haveIntersitialAd: true,
                                  )));
                    },
                  ),
                  const SizedBox(width: 30),
                  DattaHalfButtonAsset(
                    imagePath: "assets/krishna_radha2.png",
                    width: getScreenWidth(context) / 2 + 50,
                    title: "వీడియోలు",
                    text: "వీడియో రూపమ్ లో భగవత్గిత",
                    onTap: () {
                      // TpInterstitialAd.load(interstitialUnitAd, onLoadFailed: (){});
                      //Navigator.push(context,PageRouteBuilder(pageBuilder: (context, a1, a2) => const ListViewScreen(title:"భగవద్గిత వీడియోస్",futureData:getVideos,id: "https://airbushack-default-rtdb.firebaseio.com/gitaVideo/.json",haveIntersitialAd: true,)));
                      Navigator.push(
                          context,
                          PageRouteBuilder(
                              pageBuilder: (context, a1, a2) =>
                                  const ListViewScreen(
                                    title: "భగవద్గిత వీడియోస్",
                                    futureData: getVideos,
                                    id: "https://airbushack-default-rtdb.firebaseio.com/gitaVideo/.json",
                                    haveIntersitialAd: true,
                                  )));
                    },
                  )
                ]),
                Container(
                  height: 100,
                )
              ],
            ),
          ),
        ],
      ),
      //bottomNavigationBar: const TpBannerAd(bannerUnitId),
    );
  }
}
