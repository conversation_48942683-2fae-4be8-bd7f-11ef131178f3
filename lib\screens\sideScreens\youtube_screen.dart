import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';

class CustomYotubePlayer extends StatefulWidget {
  final String youtubeID;
  const CustomYotubePlayer({Key? key, required this.youtubeID})
      : super(key: key);

  @override
  _CustomYotubePlayerState createState() => _CustomYotubePlayerState();
}

class _CustomYotubePlayerState extends State<CustomYotubePlayer> {
  late YoutubePlayerController _controller;
  
  BoxDecoration outerDecoration() {
      return BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: const [
          BoxShadow(
            offset: Offset(0, 1),
            blurRadius: 4,
            color:  Colors.black,
          )
        ],
      );
    }

  @override
  void initState() {
    _controller = YoutubePlayerController.fromVideoId(
        videoId: widget.youtubeID,
        autoPlay: true,
        //YoutubePlayerController.convertUrlToId(widget.youtubeURL!)!,
        params: const YoutubePlayerParams(
            enableJavaScript: false,
            loop: true,
            showControls: true,
            showVideoAnnotations: false,
            showFullscreenButton: false,
            playsInline: false,
            color: 'transparent'));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerScaffold(
      defaultOrientations: [DeviceOrientation.portraitUp],
      controller: _controller,
      aspectRatio: 16 / 9,
      builder: (context, player) {
        return Stack(
          children: [
            Column(
              children: [
                SizedBox(height: 50,),
                Expanded(child: Container(child: player)),
              ],
            ),
            Positioned(
              top: 50,
              left: 3,
              child: TextButton(
                onPressed: (){Navigator.pop(context);},
                child: Container(
                  decoration: outerDecoration(),
                  height: 50,
                  width: 50,
                  child: Icon(Icons.arrow_back_rounded,color: Colors.black,),
                ),
              )
              ),
              Positioned(
                top: 50,
                right: 3,
                child: TextButton(
                  onPressed: (){Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (context) => FullScreenYoutubePlayer(controller: _controller,)));},
                  child: Container(
                    decoration: outerDecoration(),
                    height: 50,
                    width: 50,
                    child: Icon(Icons.screen_rotation_rounded,color: Colors.black,),
                  ),
                )
              )
          ],
        );
      },
    );
  }
}



class FullScreenYoutubePlayer extends StatelessWidget {

  final YoutubePlayerController controller;
  const FullScreenYoutubePlayer({Key? key, required this.controller}): super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return YoutubePlayerScaffold(
      defaultOrientations: [DeviceOrientation.landscapeRight],
      controller: controller,
      
      builder: (context, player) {
        return player;
      },
    );
  }
}
