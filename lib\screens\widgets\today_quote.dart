import 'dart:math';

import 'package:bhagawathgitaapp/data/quotes.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class TodayQuoteWidget extends StatefulWidget {
  TodayQuoteWidget({Key? key}) : super(key: key);

  @override
  _TodayQuoteWidgetState createState() => _TodayQuoteWidgetState();
}

class _TodayQuoteWidgetState extends State<TodayQuoteWidget> {
  final InAppReview inAppReview = InAppReview.instance;
  late String quote;
  late List<Widget> randomButtons;
  late Widget randomButton;

  @override
  void initState() {
    super.initState();
    _updateQuoteAndButton();
  }

  void _updateQuoteAndButton() {
    setState(() {
      quote = quotes[Random().nextInt(quotes.length)];
      randomButtons = [
        OutlinedButton.icon(
          onPressed: () async {
            final telegramUrl =
                Uri.parse('https://t.me/joinchat/sRGpRv1dilJiNGE1');
            if (await canLaunchUrl(telegramUrl)) {
              await launchUrl(telegramUrl);
            } else {
              throw 'Could not launch $telegramUrl';
            }
          },
          icon: const Icon(
            Icons.telegram,
            color: Colors.blue,
          ),
          label: Text(
            'Telegram లో చేరండి',
            style: GoogleFonts.mallanna(
              fontSize: 16,
            ),
          ),
          style: OutlinedButton.styleFrom(
            side: BorderSide(
              color: Colors.blue,
            ),
          ),
        ),
        OutlinedButton.icon(
          onPressed: () => _rateApp(),
          icon: const Icon(
            Icons.star,
            color: Colors.amber,
          ),
          label: Text(
            'Rate our app',
            style: GoogleFonts.mallanna(
              fontSize: 16,
            ),
          ),
          style: OutlinedButton.styleFrom(
            side: BorderSide(
              color: Colors.amber,
            ),
          ),
        ),
        OutlinedButton.icon(
          onPressed: () => shareApp(),
          icon: const Icon(
            Icons.share,
            color: Colors.green,
          ),
          label: Text(
            'Share our app',
            style: GoogleFonts.mallanna(
              fontSize: 16,
            ),
          ),
          style: OutlinedButton.styleFrom(
            side: BorderSide(
              color: Colors.green,
            ),
          ),
        ),
      ];
      randomButton = randomButtons[Random().nextInt(randomButtons.length)];
    });
  }

  Future<void> _rateApp() async {
    if (await inAppReview.isAvailable()) {
      inAppReview.openStoreListing();
    } else {
      inAppReview.openStoreListing();
    }
  }

  void shareApp() {
    String appLink =
        'https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu&hl=en';
    String shareText = '''
🌞 ఈ రోజు మంచి మాట 🌟
-------------------

$quote

--------------------
ఇలాంటి మాటలు కొరకు భగవద్గీత తెలుగు APPని ఇన్స్టాల్ చేసుకోండి
👉   $appLink
''';

    Share.share(shareText, subject: 'భగవద్గీత తెలుగు యాప్');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Spacer(),
              Text(
                '🌞 ఈ రోజు మంచి మాట 🌟',
                style: GoogleFonts.mallanna(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              Spacer(),
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          QuoteText(
            quote: quote,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: randomButton,
              ),
              const SizedBox(width: 10),
              Expanded(
                child: TextButton.icon(
                  onPressed: () => shareApp(),
                  icon: const Icon(
                    Icons.share,
                    color: Colors.black,
                  ),
                  label: Text(
                    'షేర్',
                    style: GoogleFonts.mallanna(
                      fontSize: 16,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.amber,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class QuoteText extends StatelessWidget {
  final String quote;
  const QuoteText({
    Key? key,
    required this.quote,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          constraints: const BoxConstraints(minHeight: 100),
          margin: EdgeInsets.only(bottom: 10, top: 10),
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Color.fromARGB(255, 227, 218, 247),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Text(
              quote,
              style: GoogleFonts.peddana(
                fontSize: 18,
                color: Colors.black,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Positioned(
            bottom: 0,
            right: 0,
            child: Material(
              elevation: 5,
              shape: CircleBorder(),
              color: Colors.amber,
              child: Container(
                height: 35,
                width: 35,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(Icons.format_quote, color: Colors.white, size: 18),
              ),
            )),
      ],
    );
  }
}
