import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:encrypt/encrypt.dart';
// import 'dart:convert';

class AIMessage {
  final String role;
  final String content;

  AIMessage({
    required this.role,
    required this.content,
  });

  // Convert from Map
  factory AIMessage.fromMap(Map<String, dynamic> map) {
    return AIMessage(
      role: map['role'] as String,
      content: map['content'] as String,
    );
  }

  // Convert to Map for GPT format
  Map<String, String> toGptMap() {
    return {
      'role': role,
      'content': content,
    };
  }

  // Convert to Map for Gemini format
  Map<String, String> toGeminiMap() {
    return {
      'text': content,
    };
  }
}

class AIServiceProvider {
  static const String _githubGptEndpoint = "https://models.inference.ai.azure.com";
  static const String _ourOwnAPIEndpoint = "https://ipl-live.onrender.com";
  static const String _geminiEndpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent";
  
  Future<String> getAIResponse({
    required String serviceType,
    required String apiKey,
    required List<AIMessage> messages,
    required double temperature,
  }) async {
    switch (serviceType.toLowerCase()) {
      case 'our-own-api':
        return await _getOurOwnAPIResponse( messages);
      case 'deep-sink':
        return await _getDeepSinkResponse(apiKey, messages, temperature);
      case 'github-gpt':
        return await _getGptResponse(apiKey, messages,temperature);
      case 'gemini-flash-1.5':
        return await _getGeminiResponse(apiKey, messages);
      case 'llama':
        return await _getLlamaResponse(apiKey, messages, temperature);
      default:
        if (serviceType.split("_").length==2 && serviceType.split("_")[0]=='github'){          
          return await _getGithubResponseByModelName(apiKey, messages,temperature,serviceType.split("_")[1]);
        };
        throw Exception('Unsupported AI service type: $serviceType');
    }
  }

  Future<String> _getOurOwnAPIResponse(List<AIMessage> messages) async {
    final formatedMessages = messages.map((msg) => msg.toGptMap()).toList();

    String jsonString = jsonEncode(formatedMessages);

    // AES encryption
    final key = Key.fromUtf8(
        "bhagwathgitaAPISecretTokenForAES"); // Must match the backend key
    final iv = IV.fromUtf8("garudadevdataser"); // Must match the backend IV
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc));
    final encrypted = encrypter.encrypt(jsonString, iv: iv);

    // Base64 encode the encrypted data
    String encryptedBase64 = base64Encode(encrypted.bytes);

    final response = await http.post(
      Uri.parse('$_ourOwnAPIEndpoint/chat'),
      body: encryptedBase64.toString(),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(utf8.decode(response.bodyBytes));
      return jsonResponse['choices'][0]['message']['content'];
    } else if (response.statusCode == 413){
      return "payload_exceeded";
    } else {
      throw Exception(
          'Our Own API Endpoint Error: ${response.statusCode} - ${response.body}');
    }
  }

  Future<String> _getLlamaResponse(String token, List<AIMessage> messages, double temperature) async {
    final modelName = "Meta-Llama-3.1-405B-Instruct";
    
    final response = await http.post(
      Uri.parse('$_githubGptEndpoint/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'messages': messages.map((msg) => msg.toGptMap()).toList(),
        'temperature': temperature,
        'top_p': 0.1,
        'max_tokens': 1000,
        'model': modelName,
      }),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(utf8.decode(response.bodyBytes));
      return jsonResponse['choices'][0]['message']['content'];
    } else if (response.statusCode == 413){
      return "payload_exceeded";
    } else {
      throw Exception('Llama API Error: ${response.statusCode} - ${response.body}');
    }
  }

  Future<String> _getGptResponse(String token, List<AIMessage> messages, double temperature) async {
    final modelName = "gpt-4o";
    
    final response = await http.post(
      Uri.parse('$_githubGptEndpoint/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'messages': messages.map((msg) => msg.toGptMap()).toList(),
        'temperature': temperature,
        'top_p': 0.1,
        'max_tokens': 1000,
        'model': modelName,
      }),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(utf8.decode(response.bodyBytes));
      return jsonResponse['choices'][0]['message']['content'];
    } else if (response.statusCode == 413){
      return "payload_exceeded";
    } else {
      throw Exception('GPT API Error: ${response.statusCode} - ${response.body}');
    }
  }

  Future<String> _getGithubResponseByModelName(String token, List<AIMessage> messages, double temperature,String modelName) async {
    // final modelName = "gpt-4o-mini";
    
    final response = await http.post(
      Uri.parse('$_githubGptEndpoint/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'messages': messages.map((msg) => msg.toGptMap()).toList(),
        'temperature': temperature,
        'top_p': 0.1,
        'max_tokens': 1000,
        'model': modelName,
      }),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(utf8.decode(response.bodyBytes));
      return jsonResponse['choices'][0]['message']['content'];
    } else if (response.statusCode == 413){
      return "payload_exceeded";
    } else {
      throw Exception('GPT API Error: ${response.statusCode} - ${response.body}');
    }
  }

  Future<String> _getDeepSinkResponse(String token, List<AIMessage> messages, double temperature) async {
    
    final response = await http.post(
      Uri.parse("https://api.deepseek.com/v1/chat/completions"),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'messages': messages.map((msg) => msg.toGptMap()).toList(),
        'temperature': temperature,
        'top_p': 0.1,
        'max_tokens': 1000,
        'model': "deepseek-chat",
      }),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(utf8.decode(response.bodyBytes));
      return jsonResponse['choices'][0]['message']['content'];
    } else {
      throw Exception('GPT API Error: ${response.statusCode} - ${response.body}');
    }
  }

  Future<String> _getGeminiResponse(String apiKey, List<AIMessage> messages) async {
    final contents = [{
      "parts": messages.map((msg) => msg.toGeminiMap()).toList(),
    }];

    final response = await http.post(
      Uri.parse('$_geminiEndpoint?key=$apiKey'),
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        "contents": contents,
      }),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return jsonResponse['candidates'][0]['content']['parts'][0]['text'];
    } else {
      throw Exception('Gemini API Error: ${response.statusCode} - ${response.body}');
    }
  }
}
