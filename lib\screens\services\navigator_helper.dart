import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart'; // For RewardAdService
import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:flutter/material.dart';

class NavigationHelper {
  static Future<void> navigateWithAd(
    BuildContext context,
    PageRoute route,
    NavigationAdService adService,
  ) async {
    final didShowAd = await adService.handleNavigation();
    if (!context.mounted) return;
    if (didShowAd) {
      await Future.delayed(const Duration(milliseconds: 500));
    }
    if (!context.mounted) return;
    Navigator.push(context, route);
  }
}

class RewardedNavigationHelper {
  static Future<void> navigateWithRewardedAd({
    required BuildContext context,
    required PageRoute route,
    required RewardAdService adService,
    required bool showSuccessButton,
  }) async {
    final remoteConfig = RemoteConfigService();
    final bool showRewardAds = remoteConfig.getValue<bool>(
      key: 'show_reward_ads',
      defaultValue: false,
    );

    if (showRewardAds) {
      if (!adService.isServiceInitialized) {
        print("RewardedNavigationHelper: RewardAdService not initialized. Navigating directly.");
        if (context.mounted) Navigator.push(context, route);
        return;
      }

      if (!context.mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => _StatefulRewardDialog(
          adService: adService,
          onNavigate: () {
            if (context.mounted) Navigator.push(context, route);
          },
          showSuccessButton: showSuccessButton,
        ),
      );
    } else {
      if (context.mounted) Navigator.push(context, route);
    }
  }
}

class _StatefulRewardDialog extends StatefulWidget {
  final RewardAdService adService;
  final VoidCallback onNavigate;
  final bool showSuccessButton;

  const _StatefulRewardDialog({
    required this.adService,
    required this.onNavigate,
    this.showSuccessButton = true,
  });

  @override
  State<_StatefulRewardDialog> createState() => _StatefulRewardDialogState();
}

class _StatefulRewardDialogState extends State<_StatefulRewardDialog> {
  bool _hasEarnedRewardUiState = false;
  bool _isAdLoadingFromButtonClick = false; // True when user clicks "Watch Video" and ad is showing

  VoidCallback? _adServiceListener;

  // Local message state, updated based on other states
  String _dialogMessage = 'ఆధ్యా thuật గురువు తో మాట్లాడటానికి ఈ చిన్ని వీడియో ని చూడండి 📺. ఇది ఈ యాప్ మనుగడకి సహాయం అవుతుంది 💪';

  @override
  void initState() {
    super.initState();
    _adServiceListener = () {
      if (mounted) {
        // Rebuild to reflect changes in adService.isAdLoaded or adService.hasEarnedReward
        print("_StatefulRewardDialog: AdService listener triggered. Rebuilding. isAdLoaded: ${widget.adService.isAdLoaded}");
        setState(() {
          _updateDialogMessage(); // Update message based on new state
        });
      }
    };
    widget.adService.addListener(_adServiceListener!);
    _updateDialogMessage(); // Initial message setup
    print("_StatefulRewardDialog: Initialized. isAdLoaded: ${widget.adService.isAdLoaded}");
  }

  @override
  void dispose() {
    if (_adServiceListener != null) {
      widget.adService.removeListener(_adServiceListener!);
    }
    super.dispose();
  }

  void _updateDialogMessage() {
    if (_hasEarnedRewardUiState) {
      _dialogMessage = 'ఈ వీడియో యాడ్ ని చూసినందుకు ధన్యవాదాలు 🎉🙏. ఆధ్యాత్మిక గురువుతో మాట్లాడడానికి క్రింది బటన్ నొకండి 🕉️';
    } else {
      _dialogMessage = 'ఆధ్యా thuật గురువు తో మాట్లాడటానికి ఈ చిన్ని వీడియో ని చూడండి 📺. ఇది ఈ యాప్ మనుగడకి సహాయం అవుతుంది 💪';
    }
  }

  Future<void> _watchAd() async {
    final bool adIsReadyWhenClicked = widget.adService.isAdLoaded;
    print("_StatefulRewardDialog: _watchAd called. adService.isAdLoaded at click: $adIsReadyWhenClicked");

    if (_isAdLoadingFromButtonClick) return;

    if (!adIsReadyWhenClicked) {
      // This case should ideally not happen if the button is correctly disabled.
      // If it does, it means the UI state was out of sync.
      print("_StatefulRewardDialog: ERROR - _watchAd called but adService.isAdLoaded is false. Button should have been disabled.");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ad is not ready. Please wait or try again.')),
        );
      }
      return; // Don't proceed if ad wasn't ready
    }

    setState(() {
      _isAdLoadingFromButtonClick = true;
    });

    bool adWasSuccessfullyPresented = false;
    bool rewardWasActuallyEarned = false;

    try {
      // adService.showRewardedAd() will now just return false if ad not showable,
      // without trying to load it again.
      adWasSuccessfullyPresented = await widget.adService.showRewardedAd();
      if (!mounted) return;

      if (adWasSuccessfullyPresented) {
        rewardWasActuallyEarned = widget.adService.hasEarnedReward;
        if (rewardWasActuallyEarned) {
          widget.adService.resetReward();
          if (widget.showSuccessButton) {
            setState(() {
              _hasEarnedRewardUiState = true;
              _updateDialogMessage();
            });
          } else {
            if (mounted) {
              Navigator.of(context).pop();
              widget.onNavigate();
            }
          }
        } else { // Ad shown, but no reward
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Please watch the entire video to earn the reward.')),
            );
          }
        }
      } else { // Ad FAILED TO SHOW (uncontrollable error, e.g., service returned false)
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Ad not available at the moment. Proceeding...')),
          );
          Navigator.of(context).pop();
          widget.onNavigate();
        }
      }
    } catch (e, stackTrace) { // Unhandled EXCEPTION from showRewardedAd
      print('_StatefulRewardDialog: Error showing rewarded ad: $e\n$stackTrace');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('An ad error occurred. Proceeding...')),
      );
      Navigator.of(context).pop();
      widget.onNavigate();
    } finally {
      if (mounted) {
        setState(() {
          _isAdLoadingFromButtonClick = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isSuccessState = _hasEarnedRewardUiState;
    final bool adIsCurrentlyReadyFromService = widget.adService.isAdLoaded;

    // Button is "overall loading" and thus disabled if:
    // 1. User clicked it and ad is showing (_isAdLoadingFromButtonClick)
    // OR
    // 2. Ad is NOT ready from service AND we are not in success state (i.e., we expect/need to show an ad)
    final bool isButtonDisabled = _isAdLoadingFromButtonClick || (!adIsCurrentlyReadyFromService && !isSuccessState);

    String buttonLabel;
    if (isSuccessState) {
      buttonLabel = 'Continue to Chat';
    } else if (_isAdLoadingFromButtonClick) {
      buttonLabel = 'Loading Ad...';
    } else if (!adIsCurrentlyReadyFromService) {
      buttonLabel = 'Ad Preparing...';
    } else {
      buttonLabel = 'Watch Video';
    }

    String dialogTitle;
    String dialogContent;

    if (isSuccessState) {
      dialogTitle = 'Reward Earned!';
      dialogContent = _dialogMessage; // Already updated for success
    } else if (!adIsCurrentlyReadyFromService) {
      dialogTitle = 'Preparing Ad...';
      dialogContent = 'Please wait, the ad is getting ready...';
    } else {
      dialogTitle = 'Watch Ad to Continue';
      dialogContent = _dialogMessage; // Standard prompt
    }


    return AlertDialog(
      title: Text(dialogTitle),
      content: Text(dialogContent),
      actions: <Widget>[
        if (!isSuccessState)
          TextButton(
            onPressed: _isAdLoadingFromButtonClick ? null : () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        SizedBox(
          width: isSuccessState ? double.infinity : null,
          child: ElevatedButton.icon(
            icon: isSuccessState
                ? const Icon(Icons.check)
                : Icon(isButtonDisabled ? Icons.hourglass_empty : Icons.play_arrow),
            onPressed: isSuccessState
                ? () {
                    if (mounted) {
                      Navigator.of(context).pop();
                      widget.onNavigate();
                    }
                  }
                : isButtonDisabled // Use the derived disabled state
                    ? null
                    : _watchAd,
            style: ElevatedButton.styleFrom(
              elevation: 5,
              backgroundColor: isSuccessState
                  ? Colors.green
                  : (isButtonDisabled ? Colors.grey : Theme.of(context).colorScheme.primary),
              foregroundColor: Colors.white,
            ),
            label: Text(buttonLabel),
          ),
        ),
      ],
    );
  }
}
