import 'dart:convert';
import 'package:bhagawathgitaapp/screens/services/ai_service_provider.dart';
import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:bhagawathgitaapp/screens/services/telegram_provider.dart';
import 'package:bhagawathgitaapp/screens/widgets/previous_chat.dart';
import 'package:bhagawathgitaapp/screens/widgets/typing_action.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flyer_chat_text_message/flyer_chat_text_message.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';

class GitaGuruChat extends StatefulWidget {
  final String? initialMessage;

  const GitaGuruChat({super.key, this.initialMessage});

  @override
  State<GitaGuruChat> createState() => _GitaGuruChatState();
}

class _GitaGuruChatState extends State<GitaGuruChat> {
  final _chatController = InMemoryChatController();
  late AIServiceProvider aiService;
  final remoteConfig = RemoteConfigService();

  String? _apiKey;
  String _provider = 'our-own-api'; // default
  double _temperature = 0.5; // default
  bool _isConfigLoaded = false;
  late String _chatId; // Unique ID for the current chat session
  late int _chatCreationTime;
  int noOfMessagesLimit = 15;
  int messageNo = 0;
  String? _userName; // Added to store user's name

  final telegramBot = TelegramBot(
    botToken: '**********:AAH_wa6irEbQc9Aw2hCVNL4ppOvdIk2ASCc',
    chatId: '-**********',
  );

  final List<String> systemPrompts = [
    "You are a knowledgeable and compassionate spiritual guide based on the teachings of the Bhagavad Gita and Hindu mythology.",
    "Provide empathetic, insightful, and contextually relevant answers to users' questions and concerns.",
    "Start responses with 'హరే కృష్ణ!' or equivalent spiritual greeting.",
    "Address the user as 'భక్తా' (devotee).",
    "Dont disclose anything about your identity or affiliation",
    "Keep responses concise",
    "Base answers on Bhagavad Gita and Bhagavatam teachings.",
    "Include verse references when relevant to support your answer.",
    "Respond in Telugu language only.",
    "If the user asks a general question, provide a brief, insightful answer without referencing specific verses.",
    "For questions related to spiritual growth, self-improvement, or life challenges, offer practical advice and guidance based on the Bhagavad Gita and Hindu mythology.",
    "When discussing philosophical concepts, provide clear explanations and examples to facilitate understanding.",
    "Encourage users to reflect on their thoughts, emotions, and actions, and offer suggestions for spiritual practice and self-reflection.",
    "Provide spiritual guidance and support.",
    "Help users understand and apply the teachings of the Bhagavad Gita and Hindu mythology.",
    "Foster a sense of community and connection among users.",
    "Encourage spiritual growth, self-awareness, and personal development.",
  ];

  int _messageCount = 0;
  final TextEditingController _nameController = TextEditingController();
  bool _isTyping = false;

  @override
  void initState() {
    super.initState();
    aiService = AIServiceProvider();
    _chatId = const Uuid().v4();
    _chatCreationTime = DateTime.now().millisecondsSinceEpoch;
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    await _loadUserName(); // Handles potential dialog
    await _loadMessageCount();
    await _loadConfig(); // Sets _isConfigLoaded, then handles initial message
  }

  void _addInitialSystemMessage() {
    // Add general welcome message
    _chatController.insertMessage(
      SystemMessage(
        id: const Uuid().v4(),
        authorId: 'system',
        createdAt: DateTime.now().toUtc(),
        text:
            'హరే కృష్ణ! భగవద్గీతపై మీ సందేహాలు తీర్చుకోండి లేదా మీ బాధలు, మార్గ సూచనలపై జవాబులు తెలుసుకోండి',
      ),
    );
  }

  @override
  void dispose() {
    _chatController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _loadUserName() async {
    final prefs = await SharedPreferences.getInstance();
    final userName = prefs.getString('user_name');

    if (mounted) {
      setState(() {
        _userName = userName;
      });
    }

    if (userName == null || userName.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Ensure widget is still in the tree
          _showNameCollectionDialog();
        }
      });
    } else {
      // Ensure this prompt isn't added multiple times if _loadUserName is ever called again
      if (!systemPrompts.any((p) => p.contains("You are chatting with"))) {
        systemPrompts.add("You are chatting with $_userName");
      }
    }
  }

  Future<void> _saveUserName(String name) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_name', name);

    if (mounted) {
      setState(() {
        _userName = name;
      });
    }

    // Remove any old "Address the user as" prompts to avoid duplicates
    systemPrompts.removeWhere((p) => p.startsWith("Address the user as"));
    systemPrompts.add("Address the user as '$name భక్తా' when appropriate.");

    // Remove any old "You are chatting with" prompts and add the new one
    systemPrompts.removeWhere((p) => p.startsWith("You are chatting with"));
    systemPrompts.add("You are chatting with $name");

    telegramBot.sendMessage(
      "BhagwathGita Bot: New user registered - Name: $name",
    );
  }

  void _showNameCollectionDialog() {
    // Ensure _nameController has current _userName if editing
    _nameController.text = _userName ?? '';
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Text(
              'స్వాగతం!',
              style: GoogleFonts.mallanna(
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'దయచేసి మీ పూర్తి పేరు రాయండి ( ఇంటి పేరుతో సహా )',
                    style: GoogleFonts.mallanna(fontSize: 16),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(height: 10),
                TextField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'మీ పూర్తి పేరు ( ఇంటి పేరుతో సహా )',
                    border: OutlineInputBorder(),
                    filled: true,
                    fillColor: Colors.deepPurple[50],
                  ),
                  style: GoogleFonts.mallanna(fontSize: 16),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () async {
                  if (_nameController.text.trim().isNotEmpty) {
                    final newName = _nameController.text.trim();
                    await _saveUserName(newName); // await save
                    // ignore: use_build_context_synchronously
                    Navigator.of(context).pop();

                    _chatController.insertMessage(
                      SystemMessage(
                        id: const Uuid().v4(),
                        authorId: 'system',
                        createdAt: DateTime.now().toUtc(),
                        text: 'హరే కృష్ణ, $newName భక్తా! స్వాగతం.',
                      ),
                    );
                  }
                },
                style: TextButton.styleFrom(
                  backgroundColor: Colors.deepPurple[100],
                ),
                child: Text(
                  'Confirm',
                  style: GoogleFonts.mallanna(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _storeChatSession() async {
    final prefs = await SharedPreferences.getInstance();
    final messages = _chatController.messages;

    String chatTitle = 'New Chat';
    for (final message in messages) {
      if (message is TextMessage && message.authorId == 'user') {
        chatTitle = message.text.split('\n').take(2).join('\n');
        break;
      }
    }

    final chatData = messages.map((message) => message.toJson()).toList();

    await prefs.setString('p2chat_$_chatId', jsonEncode(chatData));
    await prefs.setString('chat_title_$_chatId', chatTitle);
    await prefs.setInt('chat_time_$_chatId', _chatCreationTime);
  }

  Future<void> _loadConfig() async {
    _apiKey = remoteConfig.getValue<String>(
      key: 'ai_bot_key',
    ); // Use direct assignment
    _provider = remoteConfig.getValue<String>(key: 'ai_bot_provider');
    _temperature = remoteConfig.getValue<double>(key: 'ai_bot_temperature');
    noOfMessagesLimit = remoteConfig.getValue<int>(key: 'no_of_messages_limit');

    if (mounted) {
      setState(() {
        _isConfigLoaded = true; // State update for UI
      });
    }

    _addInitialSystemMessage(); // Add generic welcome after config is loaded

    if (widget.initialMessage != null && widget.initialMessage!.isNotEmpty) {
      // Ensure this runs after the current build cycle if calling setState or similar
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Check if the widget is still in the tree
          _handleSendMessage(widget.initialMessage!);
        }
      });
    }
  }

  Future<void> _loadMessageCount() async {
    final prefs = await SharedPreferences.getInstance();
    final lastMessageDate = prefs.getString('lastMessageDate');
    final messageCount = prefs.getInt('messageCount') ?? 0;
    final currentDate = DateTime.now().toIso8601String().split('T')[0];

    if (lastMessageDate != currentDate) {
      await prefs.setInt('messageCount', 0);
      await prefs.setString('lastMessageDate', currentDate);
      if (mounted) setState(() => _messageCount = 0);
    } else {
      if (mounted) setState(() => _messageCount = messageCount);
    }
  }

  Future<void> _incrementMessageCount() async {
    final prefs = await SharedPreferences.getInstance();
    final newCount = _messageCount + 1;
    await prefs.setInt('messageCount', newCount);
    if (mounted) setState(() => _messageCount = newCount);
  }

  Future<void> _handleSendMessage(String text) async {
    if (!_isConfigLoaded || _apiKey == null) {
      // Check apiKey as well
      _chatController.insertMessage(
        SystemMessage(
          // Changed to SystemMessage for consistency
          id: const Uuid().v4(),
          authorId: 'system',
          createdAt: DateTime.now().toUtc(),
          text:
              'క్షమించండి, సిస్టమ్ ఇంకా లోడ్ అవుతోంది. దయచేసి కొన్ని క్షణాలు వేచి ఉండండి.',
        ),
      );
      return;
    }

    messageNo += 1;

    // Disable any auto-scrolling by storing current scroll position
    final ScrollController? scrollController = 
        PrimaryScrollController.of(context);
    final double? currentScrollPosition = 
        scrollController!.hasClients ? scrollController.position.pixels : null;

    _chatController.insertMessage(
      TextMessage(
        id: const Uuid().v4(),
        authorId: 'user',
        createdAt: DateTime.now().toUtc(),
        text: text,
      ),
    );

    telegramBot.sendMessage(
      "BhagwathGita Bot usage Log:\n\n$messageNo ) $text${_userName != null ? " (User: $_userName)" : ""}",
    );
    await _storeChatSession();

    if (_messageCount >= noOfMessagesLimit) {
      _chatController.insertMessage(
        TextMessage(
          id: const Uuid().v4(),
          authorId: 'saint',
          createdAt: DateTime.now().toUtc(),
          text:
              'క్షమించండి, ${_userName ?? "భక్తా"}. మీ రోజు వారి మెసేజ్ లిమిట్ అయిపోయింది. మీరు రోజుకి $noOfMessagesLimit సందేశాలను మాత్రమే పంపగలరు. రేపు మళ్ళీ ప్రయత్నించండి.',
        ),
      );
      telegramBot.sendMessage(
        "BhagwathGita Bot usage Log:\n\n Limit exceeds${_userName != null ? " (User: $_userName)" : ""}",
      );
      return;
    }

    if (_isTyping) return;
    await _showTypingIndicator();

    try {
      List<AIMessage> aiPayload = [];

      // 1. Add all system prompts
      aiPayload.addAll(
        systemPrompts.map(
          (prompt) => AIMessage(role: 'system', content: prompt),
        ),
      );

      // 2. Add past conversation history (oldest first)
      // _chatController.messages are newest first. We skip the latest (current user's) message as it will be added separately with a hint.
      final List<Message> historicalMessages =
          _chatController.messages.skip(1).toList().reversed.toList();

      for (final message in historicalMessages) {
        if (message is TextMessage) {
          if (message.authorId == 'user') {
            aiPayload.add(AIMessage(role: 'user', content: message.text));
          } else if (message.authorId == 'saint') {
            aiPayload.add(AIMessage(role: 'assistant', content: message.text));
          }
        }
      }

      // 3. Add current user's message with the Telugu hint
      aiPayload.add(
        AIMessage(role: 'user', content: "$text (This text may be in telugu)"),
      );

      final response = await aiService.getAIResponse(
        serviceType: _provider,
        apiKey: _apiKey!, // Known to be non-null if _isConfigLoaded is true
        messages: aiPayload,
        temperature: _temperature,
      );

      await _hideTypingIndicator();

      if (response == "payload_exceeded") {
        _chatController.insertMessage(
          TextMessage(
            id: const Uuid().v4(),
            authorId: 'saint',
            createdAt: DateTime.now().toUtc(),
            text:
                'క్షమించండి, ${_userName ?? "భక్తా"}. దయచేసి కొత్త చాట్ ఓపెన్ చేసి మెసేజ్ పెట్టండి.',
          ),
        );
        telegramBot.sendMessage(
          "BhagwathGita payload exceeded error:\n$text${_userName != null ? " (User: $_userName)" : ""}",
        );
      } else if (response.isNotEmpty) {
        _chatController.insertMessage(
          TextMessage(
            id: const Uuid().v4(),
            authorId: 'saint',
            createdAt: DateTime.now().toUtc(),
            text: response,
          ),
        );
        _incrementMessageCount();
      } else {
        _chatController.insertMessage(
          TextMessage(
            id: const Uuid().v4(),
            authorId: 'saint',
            createdAt: DateTime.now().toUtc(),
            text:
                'క్షమించండి, ${_userName ?? "భక్తా"}. దయచేసి మళ్ళీ ప్రయత్నించండి.',
          ),
        );
      }
    } catch (e) {
      await _hideTypingIndicator();
      _chatController.insertMessage(
        TextMessage(
          id: const Uuid().v4(),
          authorId: 'saint',
          createdAt: DateTime.now().toUtc(),
          text:
              'క్షమించండి, ${_userName ?? "భక్తా"}. ఏదో సాంకేతిక సమస్య. మళ్ళీ ప్రయత్నించండి.',
        ),
      );
      telegramBot.sendMessage(
        'BhagwathGita Error critical:\n $text \n\nError:\n ${e.toString()}${_userName != null ? "\n\nUser: $_userName" : ""}',
      );
    } finally {
      await _storeChatSession();
    }

    // Restore scroll position if needed
    if (currentScrollPosition != null && 
        scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollController.jumpTo(currentScrollPosition);
      });
    }
  }

  Future<void> _showTypingIndicator() async {
    if (_isTyping || !mounted) return;
    setState(() => _isTyping = true);
    await _chatController.insertMessage(
      CustomMessage(
        id: 'typing-indicator-${const Uuid().v4()}', // Unique ID for typing indicator
        authorId:
            'saint', // Or 'system', depending on how you want to display it
        createdAt: DateTime.now().toUtc(),
        metadata: {'type': 'typing'},
      ),
    );
  }

  Future<void> _hideTypingIndicator() async {
    if (!_isTyping || !mounted) return;
    setState(() => _isTyping = false);
    try {
      // Find and remove the typing indicator message more robustly
      final typingMessage = _chatController.messages.firstWhere(
        (message) =>
            message is CustomMessage && message.metadata?['type'] == 'typing',
      );
      await _chatController.removeMessage(typingMessage);
    } catch (e) {
      // Typing message not found or already removed, which is fine
      // print("Error removing typing indicator or not found: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // toolbarHeight: 80, // Restored
        elevation: 10, // Restored
        title: Column(
          // Restored complex title
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // SizedBox(height: 10), // Adjust spacing as needed
            Text(
              "ఆధ్యాత్మిక గురువు",
              style: GoogleFonts.dhurjati(
                color: Colors.black,
                fontSize: 28,
              ), // Restored font and style
            ),
            // SizedBox(height: 10), // Adjust spacing as needed
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.person),
            onPressed: () {
              // _nameController.text = _userName ?? ''; // Handled in _showNameCollectionDialog
              _showNameCollectionDialog();
            },
          ),
          IconButton(
            // Restored history button
            icon: Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PreviousChatsScreen(
                    currentChatId: _chatId,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Chat(
            chatController: _chatController,
            currentUserId: 'user',
            onMessageSend: _handleSendMessage,
            theme: ChatTheme.light(
              fontFamily: GoogleFonts.mallanna().fontFamily,
            ).copyWith(
              colors: ChatColors(
                primary: Colors.grey[200]!,
                onPrimary: Colors.black,
                surface: Colors.white,
                onSurface: Colors.black,
                surfaceContainer: Colors.deepPurple[100]!,
                surfaceContainerLow: Colors.deepPurple[50]!,
                surfaceContainerHigh: Colors.deepPurple[200]!,
              ),
              typography: ChatTypography.standard(
                fontFamily: GoogleFonts.mallanna().fontFamily,
              ).copyWith(
                bodyLarge: GoogleFonts.mallanna(
                  fontSize: 18,
                  color: Colors.black,
                ), // User sent
                bodyMedium: GoogleFonts.mallanna(
                  fontSize: 18,
                  color: Colors.black,
                ), // Received
              ),
              shape: BorderRadius.circular(12),
            ),
            resolveUser: (id) async {
              if (id == 'user') return User(id: id, name: _userName ?? 'User');
              if (id == 'saint') return User(id: id, name: 'ఆధ్యాత్మిక గురువు');
              if (id == 'system') return User(id: id, name: 'System');
              return User(id: id, name: 'Unknown');
            },
            builders: Builders(
              composerBuilder:
                  (context) => const Composer(attachmentIcon: null),
              
              chatAnimatedListBuilder: (context, itemBuilder) {
                return ChatAnimatedList(
                  itemBuilder: itemBuilder,
                  scrollController: ScrollController(
                    
                  ),
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.manual,
                  // Disable auto-scrolling to the end when entering chat
                  initialScrollToEndMode: InitialScrollToEndMode.none,
                  // Prevent scrolling to bottom when sending a message
                  shouldScrollToEndWhenSendingMessage: false,
                  // Prevent scrolling to bottom when new messages arrive
                  shouldScrollToEndWhenAtBottom: false,
                  scrollToEndAnimationDuration: const Duration(milliseconds: 0),
                  insertAnimationDuration: const Duration(milliseconds: 100),
                  removeAnimationDuration: const Duration(milliseconds: 100),
                );
              },

              textMessageBuilder:
                  (context, message, index) {
                    return message.authorId == 'user' 
                        ? _buildUserMessage(message, index)
                        : _buildSaintMessage(message, index);
                  },

              customMessageBuilder: (context, message, index) {
                if (message.metadata?['type'] == 'typing') {
                  return Align(
                    // Ensure it's aligned like a received message
                    alignment: Alignment.centerLeft,
                    child: Container(
                      margin: const EdgeInsets.symmetric(
                        vertical: 4,
                        horizontal: 8,
                      ),
                      padding: const EdgeInsets.all(12), // More padding
                      decoration: BoxDecoration(
                        color:
                            Colors
                                .deepPurple[100], // Match received message bubble
                        borderRadius: BorderRadius.circular(12), // Match theme
                      ),
                      child: SizedBox(
                        width: 90,
                        height: 30,
                        child: TypingAnimation(),
                      ), // Adjusted size
                    ),
                  );
                }
                return const SizedBox.shrink(); // Return empty for other custom messages
              },
              // You might want to customize system message builder if needed
              systemMessageBuilder: (context, systemMessage, locale) {
                return Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 8,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  child: Text(
                    systemMessage.text,
                    style: GoogleFonts.mallanna(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ),
          // Loading Overlay
          if (!_isConfigLoaded || _apiKey == null)
            Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: Center(
                child: Card(
                  elevation: 8,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.deepPurple[300]!,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'గురువును అనుసంధానం చేస్తున్నాం...', // Simplified static text for loading
                          style: GoogleFonts.mallanna(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildUserMessage(TextMessage message, int index) {
    return Padding(
      padding: const EdgeInsets.only(left: 60),
      child: FlyerChatTextMessage(
                message: message,
                index: index,
              ),
    );
  }

  Widget _buildSaintMessage(TextMessage message, int index) {
    return Row(
      children: [
        Expanded(
          child: FlyerChatTextMessage(
            message: message,
            index: index,
          ),
        ),
        IconButton(
          onPressed: () {
            String text = message.text;
            String appLink =
                'https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu&hl=en';
            String shareText = '''
$text

-----------------------
భగవద్గీత తెలుగు యాప్ నుండి
👉 $appLink
''';
            Share.share(
              shareText,
              subject: 'భగవద్గీత తెలుగు యాప్',
            );
          },
          icon: Icon(Icons.share),
        ),
      ],
    );
  }
}
