import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<bool> isFavorite(chapterNo,slokaNo) async {
  SharedPreferences prefs=await SharedPreferences.getInstance();
  List l=prefs.getStringList("favSlok")??[];
  if (l.contains("$chapterNo:$slokaNo")){return true;}
  return false;
}

Future addFavorite(chapterNo,slokaNo) async {
  SharedPreferences prefs=await SharedPreferences.getInstance();
  List<String> l=prefs.getStringList("favSlok")??[];
  String toastMsg="added to favorites";
  if (l.contains("$chapterNo:$slokaNo")) {l.remove("$chapterNo:$slokaNo");
    toastMsg="removed from favorites";}
  else {l.add("$chapterNo:$slokaNo");}
  prefs.setStringList("favSlok", l);
  Fluttertoast.showToast(msg: toastMsg);
}

Future<List<String>> getAllFavorites() async {
  SharedPreferences prefs=await SharedPreferences.getInstance();
  List<String> l=prefs.getStringList("favSlok")??[];
  return l;
}
