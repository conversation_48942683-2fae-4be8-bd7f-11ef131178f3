import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:marquee/marquee.dart';


class DattaFloatingText extends StatefulWidget {
  final Function? futureData;
  final String? text;
  const DattaFloatingText({Key? key,this.futureData,this.text}):assert(
          (text != null || futureData != null),
          'Either scrolling text or Shared Preference function should be provided',
        ),super(key: key);

  @override
  _DattaFloatingTextState createState() => _DattaFloatingTextState();
}

class _DattaFloatingTextState extends State<DattaFloatingText> {
  
  SizedBox loading(context){
    return SizedBox(
      height: 25,
      child: Marquee(
        text: "భగవత్గీత కోసం తెలుగులో ఉత్తమ అప్లికేషన్...   ",
        style: GoogleFonts.mallanna(
          fontWeight: FontWeight.w500,
          fontSize: 19,
          height: 1.3,
        ),
        scrollAxis: Axis.horizontal,
        crossAxisAlignment: CrossAxisAlignment.start,
        blankSpace: 20.0,
        velocity: 50,
        pauseAfterRound: const Duration(seconds: 1),
        showFadingOnlyWhenScrolling: true,
        fadingEdgeStartFraction: 0.1,
        fadingEdgeEndFraction: 0.1,
        startPadding: 10.0,
        accelerationDuration:const Duration(seconds: 1),
        accelerationCurve: Curves.linear,
        decelerationDuration:const Duration(milliseconds: 500),
        decelerationCurve: Curves.easeOut,
      ),
    );
  }

  SizedBox floatingText(text) {
    double velocity = 50;
    return SizedBox(
      height: 25,
      child: Marquee(
        text: text,
        style: GoogleFonts.mallanna(
          fontWeight: FontWeight.w500,
          fontSize: 19,
          height: 1.3,
        ),
        scrollAxis: Axis.horizontal,
        crossAxisAlignment: CrossAxisAlignment.start,
        blankSpace: 20.0,
        velocity: velocity,
        pauseAfterRound: const Duration(seconds: 1),
        showFadingOnlyWhenScrolling: true,
        fadingEdgeStartFraction: 0.1,
        fadingEdgeEndFraction: 0.1,
        startPadding: 10.0,
        accelerationDuration:const Duration(seconds: 1),
        accelerationCurve: Curves.linear,
        decelerationDuration:const Duration(milliseconds: 500),
        decelerationCurve: Curves.easeOut,
      ),
    );
  }

  Future<String> getText() async{
    return widget.text!;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: (widget.text==null)?widget.futureData!():getText(),
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasData) {
          return floatingText(snapshot.data);
        }
        else{
          return loading(context);
        }
      });
  }
}