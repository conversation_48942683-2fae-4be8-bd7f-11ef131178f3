import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:bhagawathgitaapp/model/utils.dart';

class DattaFullButtonAsset extends StatelessWidget {
  final String? imagePath;
  final String title;
  final String text;
  final double? height;
  final double? width;
  final VoidCallback onTap;
  final isReversed;

  const DattaFullButtonAsset({
    Key? key,
    this.imagePath,
    required this.title,
    required this.text,
    this.height = 150,
    this.width,
    this.isReversed = false,
    required this.onTap,
  }) : super(key: key);

  BoxDecoration get _decoration => BoxDecoration(
        color: Colors.transparent,
        boxShadow:  [
          BoxShadow(
            offset: Offset(0, 8),
            blurRadius: 24,
            color: Color(0xFFB7B7B7).withValues(alpha: 0.16),
          )
        ],
      );

  Widget _imageArea(BuildContext context) => Image.asset(
        imagePath!,
        width: _getWidth(context) / 3,
        fit: BoxFit.fill,
      );

  double _getWidth(BuildContext context) => width ?? getScreenWidth(context);

  Widget _textArea(BuildContext context) => Container(
        padding: const EdgeInsets.only(top: 20, left: 15, right: 10, bottom: 5),
        width: imagePath == null
            ? _getWidth(context) - 20
            : 2 * _getWidth(context) / 3 - 20,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Text(
              title,
              style: GoogleFonts.dhurjati(
                fontWeight: FontWeight.w200,
                fontSize: 20,
                height: 1.3,
              ),
            ),
            const Spacer(),
            Text(
              text,
              style: GoogleFonts.mallanna(
                fontSize: 15,
                height: 1.3,
              ),
            ),
            const Spacer(),
            const Align(
              alignment: Alignment.bottomRight,
              // child: Icon(Icons.arrow_forward_ios)
            ),
          ],
        ),
      );

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: const EdgeInsets.all(10),
      decoration: _decoration,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25.0),
        child: InkWell(
          onTap: onTap,
          child: Container(
            color: Colors.white,
            height: height,
            child: Row(
              children: (isReversed)?
              [
                 _textArea(context),
                if (imagePath != null) _imageArea(context),
              ]
              :[
                if (imagePath != null) _imageArea(context),
                _textArea(context)
              ],
            ),
          ),
        ),
      ),
    );
  }
}

