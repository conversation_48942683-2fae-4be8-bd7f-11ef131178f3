// import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/screens/ads/interstital_ads.dart';
// import 'package:bhagawathgitaapp/constraints.dart';
import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ListViewScreen extends StatefulWidget {
  final String title;
  final String? id;
  final Function futureData;
  final bool haveIntersitialAd;

  const ListViewScreen({ 
    Key? key,
    this.id,
    required this.title,
    required this.futureData,
    this.haveIntersitialAd=false,
    }) : super(key: key);

  @override
  State<ListViewScreen> createState() => _ListViewScreenState();
}

class _ListViewScreenState extends State<ListViewScreen> {

  @override
  void initState(){
    super.initState();
    if (widget.haveIntersitialAd){
      loadIntersialAd();
    }
  }

  Future loadIntersialAd() async{
    // TpInterstitialAd.load(interstitialUnitAd, onLoadFailed: (){});
    await Future.delayed(const Duration(milliseconds: 300));
    // TpInterstitialAd.show(onDismissAd: (){});
  }

  void refersh(){
    setState(() {});
  }

  Future loadData(context) async{
    if (widget.id!=null){
      return await widget.futureData(context,widget.id,refersh);}
    return await widget.futureData(context,refersh);
  }

  IconButton backButton(context){
    return IconButton(
            onPressed: () {Navigator.pop(context);},
            icon: const Icon(Icons.arrow_back,color: Colors.black,)
            );
  }

  Container loading(context){
    return Container(
            color: Colors.white,
            height: MediaQuery.of(context).size.height,
            child: SizedBox(
              height: MediaQuery.of(context).size.height - 230,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
  }

  ListTile listTile(data,index){
    return ListTile(
      tileColor: Colors.white,
      title: Center(child: data[index].text),
      leading:data[index].icon,
      trailing: data[index].endIcon,
      onTap: (){data[index].action();},);
  }

  FutureBuilder body(context){
    return FutureBuilder(
      future: loadData(context),
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasData) {
          if (snapshot.data.length!=0){
          return ListView.builder(
                itemCount: snapshot.data.length,
                itemBuilder: (context, index) {
                  return Container(
                    decoration: const BoxDecoration(border: Border(bottom: BorderSide(width: 0.5)),),
                    child: listTile(snapshot.data,index));
                }
          );}else{
            return Center(child: Image.asset("assets/empty.png"),);
          }
        }
        else{
          return loading(context);
        }
      });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 10,
        backgroundColor: Colors.white,
        leading: backButton(context),
        title: Center(child: Text(widget.title,style:GoogleFonts.peddana(color: Colors.black,fontSize: 22),)),
      ),
      body: body(context),
      bottomNavigationBar: BannerAdWidget(location: "banner_listView_screen",subLocation: widget.title,) ,
      //bottomNavigationBar: const TpBannerAd(bannerUnitId),
    );
  }
}