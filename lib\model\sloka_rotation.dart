Map slokaCountData={1: 47, 2: 72, 3: 43, 4: 42, 5: 29, 6: 47, 7: 30, 8: 28, 9: 34, 10: 42, 11: 55, 12: 20, 13: 34, 14: 27, 15: 20, 16: 24, 17: 28, 18: 78};

leftRotate(chapterNo,slokaNo){
  if (slokaNo==1){
    if (chapterNo==1){
      return [18,78];
    }
    return [chapterNo-1,slokaCountData[chapterNo-1]];}
  return [chapterNo,slokaNo-1];
}

rightRotate(chapterNo,slokaNo){
  if (slokaNo+1>slokaCountData[chapterNo]){
    if (chapterNo==18){
      return [1,1];
    }
    return [chapterNo+1,1];}
  return [chapterNo,slokaNo+1];
}

int leftRotateCustom(int index,int length){
  if (index==0){return length-1;}
  return index-1;
}

int rightRotateCustom(int index,int length){
  if ((length-1)==index){return 0;}
  return index+1;
}