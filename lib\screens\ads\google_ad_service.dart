
import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class BannerAdService extends ChangeNotifier {
  final RemoteConfigService _remoteConfigService;

  // Ad IDs - Replace with your actual ad unit IDs
  
  String get _bannerAdUnitId => 
    _remoteConfigService.getValue<String>(
      key: 'banner_ad_uid'
    );


  BannerAdService(this._remoteConfigService);
  // Map to store multiple banner ads
  final Map<String, BannerAd?> _bannerAds = {};
  final Map<String, bool> _bannerAdLoadStatus = {};
  
  InterstitialAd? _interstitialAd;

  bool isAdLoaded(String location) => _bannerAdLoadStatus[location] ?? false;
  BannerAd? getBannerAd(String location) => _bannerAds[location];

  // Initialize the service
  Future<void> initialize() async {
    await MobileAds.instance.initialize();
  }

  // Load banner ad for a specific location
  void loadBannerAd(String location) {
    // If ad already exists and is loaded, don't create a new one
    if (_bannerAds[location] != null && _bannerAdLoadStatus[location] == true) {
      return;
    }

    // Dispose existing ad if it exists but isn't loaded
    _bannerAds[location]?.dispose();

    _bannerAds[location] = BannerAd(
      adUnitId: _bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          _bannerAdLoadStatus[location] = true;
          notifyListeners();
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
          _bannerAdLoadStatus[location] = false;
          _bannerAds[location] = null;
          notifyListeners();
          print('Banner ad failed to load: $error');
        },
      ),
    );

    _bannerAds[location]?.load();
  }

  


  @override
  void dispose() {
    for (var ad in _bannerAds.values) {
      ad?.dispose();
    }
    _bannerAds.clear();
    _interstitialAd?.dispose();
    super.dispose();
  }
}

class NavigationAdService extends ChangeNotifier {
  final RemoteConfigService _remoteConfigService;
  
  String get _interstitialAdUnitId => 
    _remoteConfigService.getValue<String>(
      key: 'interstitial_ad_uid', 
      defaultValue: 'ca-app-pub-3940256099942544/1033173712'
    );
  
  bool get _shouldShowAds => 
    _remoteConfigService.getValue<bool>(
      key: 'show_interstitial_ads', 
      defaultValue: false
    );
  
  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdLoaded = false;
  DateTime? _lastAdShow;
  int _screenCountSinceLastAd = 0;
  
  // Configuration from Remote Config or defaults
  int get MIN_INTERVAL_SECONDS => 
    _remoteConfigService.getValue<int>(
      key: 'interstitial_min_ad_interval_seconds', 
      defaultValue: 30
    );

  int get SCREENS_BETWEEN_ADS => 
    _remoteConfigService.getValue<int>(
      key: 'interstitial_screens_between_ads', 
      defaultValue: 3
    );

  NavigationAdService(this._remoteConfigService);

  bool get isAdLoaded => _isInterstitialAdLoaded;

  // Initialize and load first ad
  void initialize() {
    if (_shouldShowAds) {
      loadInterstitialAd();
    }
  }

  Future<void> handleContentUpdate() async {
    if (!_shouldShowAds) return;

    _screenCountSinceLastAd++;
    
    if (_shouldShowAd()) {
      try {
        await _interstitialAd?.show();
        _screenCountSinceLastAd = 0;
      } catch (e) {
        print('Error showing ad: $e');
        loadInterstitialAd();
      }
    }
  }

  void loadInterstitialAd() {
    if (!_shouldShowAds) return;

    InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdLoaded = true;
          
          // Set up full-screen callbacks
          _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (ad) {
              ad.dispose();
              _isInterstitialAdLoaded = false;
              _lastAdShow = DateTime.now();
              loadInterstitialAd();
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              ad.dispose();
              _isInterstitialAdLoaded = false;
              loadInterstitialAd();
            },
          );
          
          notifyListeners();
        },
        onAdFailedToLoad: (error) {
          print('Interstitial ad failed to load: $error');
          _isInterstitialAdLoaded = false;
          notifyListeners();
          
          // Retry loading after a delay
          Future.delayed(const Duration(minutes: 1), loadInterstitialAd);
        },
      ),
    );
  }

  // Check if we should show an ad based on timing and frequency
  bool _shouldShowAd() {
    if (!_shouldShowAds || !_isInterstitialAdLoaded || _interstitialAd == null) return false;

    // Check minimum time interval
    if (_lastAdShow != null) {
      final timeSinceLastAd = DateTime.now().difference(_lastAdShow!).inSeconds;
      if (timeSinceLastAd < MIN_INTERVAL_SECONDS) return false;
    }

    // Check screen count
    if (_screenCountSinceLastAd < SCREENS_BETWEEN_ADS) return false;

    return true;
  }

  // Handle navigation and show ad if appropriate
  Future<bool> handleNavigation() async {
    if (!_shouldShowAds) return false;

    _screenCountSinceLastAd++;
    
    if (_shouldShowAd()) {
      try {
        await _interstitialAd!.show();
        _screenCountSinceLastAd = 0;
        return true;
      } catch (e) {
        print('Error showing ad: $e');
        loadInterstitialAd();
        return false;
      }
    }
    return false;
  }

  void resetScreenCount() {
    _screenCountSinceLastAd = 0;
  }

  @override
  void dispose() {
    _interstitialAd?.dispose();
    super.dispose();
  }
}

class RewardAdService extends ChangeNotifier {
  final RemoteConfigService _remoteConfigService;

  String _rewardedAdUnitId = "";
  bool _shouldShowAds = false;

  RewardedAd? _rewardedAd;
  bool _isRewardedAdLoaded = false;
  bool _hasEarnedRewardInternal = false;

  bool _isInitializing = false;
  bool _isInitialized = false;

  RewardAdService(this._remoteConfigService);

  bool get isAdLoaded => _isRewardedAdLoaded;
  bool get hasEarnedReward => _hasEarnedRewardInternal;
  bool get isServiceInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized || _isInitializing) {
      print('RewardAdService: Already initialized or initializing.');
      return;
    }
    _isInitializing = true;
    print('RewardAdService: Initializing...');

    _rewardedAdUnitId = _remoteConfigService.getValue<String>(
      key: 'reward_ad_uid',
      defaultValue: "",
    );
    _shouldShowAds = _remoteConfigService.getValue<bool>(
      key: 'show_reward_ads',
      defaultValue: false,
    );

    print('RewardAdService: Fetched config - shouldShowAds: $_shouldShowAds, adUnitId: $_rewardedAdUnitId');

    if (_shouldShowAds && _rewardedAdUnitId.isNotEmpty) {
      loadRewardedAd(); // Initial ad load
    } else {
      print('RewardAdService: Ads disabled or ad unit ID empty. Not loading initial ad.');
    }

    _isInitialized = true;
    _isInitializing = false;
    // No need to notifyListeners here unless some UI directly depends on _isInitialized itself.
    // Ad load status will trigger its own notifyListeners.
    print('RewardAdService: Initialization complete.');
  }

  void loadRewardedAd() {
    if (!_isInitialized) {
      print('RewardAdService: Load called, but service not initialized.');
      return;
    }
    if (!_shouldShowAds || _rewardedAdUnitId.isEmpty) {
      print('RewardAdService: Load called, but ads disabled or Ad Unit ID empty.');
      return;
    }
    if (_rewardedAd != null || _isRewardedAdLoaded) { // Check both to prevent issues
      print('RewardAdService: Load called, but an ad object exists or ad is already loaded.');
      return;
    }

    print('RewardAdService: Attempting to load ad with ID: $_rewardedAdUnitId');
    RewardedAd.load(
      adUnitId: _rewardedAdUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          print('RewardAdService: Ad loaded successfully. Ad object: $ad');
          _rewardedAd = ad;
          _isRewardedAdLoaded = true;

          _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (RewardedAd ad) {
              print('RewardAdService: Ad showed full screen content.');
            },
            onAdDismissedFullScreenContent: (ad) {
              print('RewardAdService: Ad dismissed.');
              ad.dispose();
              _rewardedAd = null;
              _isRewardedAdLoaded = false;
              _hasEarnedRewardInternal = false; // Reset reward if ad dismissed without earning
              notifyListeners(); // Important: notify UI about ad state change
              if (_shouldShowAds && _isInitialized) loadRewardedAd(); // Preload next
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              print('RewardAdService: Ad failed to show: $error');
              ad.dispose();
              _rewardedAd = null;
              _isRewardedAdLoaded = false;
              _hasEarnedRewardInternal = false; // Reset reward state
              notifyListeners(); // Important: notify UI
              if (_shouldShowAds && _isInitialized) loadRewardedAd(); // Preload next
            },
            onAdImpression: (RewardedAd ad) {
              print('RewardAdService: Ad impression.');
            },
          );
          notifyListeners(); // Ad is loaded and ready
        },
        onAdFailedToLoad: (error) {
          print('RewardAdService: Ad failed to load: $error');
          _rewardedAd = null; // Ensure it's null
          _isRewardedAdLoaded = false;
          notifyListeners(); // Notify UI about load failure

          if (_shouldShowAds && _isInitialized) {
            Future.delayed(const Duration(seconds: 60), () { // Retry after 60 seconds
              if (_isInitialized && _shouldShowAds && _rewardedAd == null && !_isRewardedAdLoaded) {
                print('RewardAdService: Retrying ad load after failure.');
                loadRewardedAd();
              }
            });
          }
        },
      ),
    );
  }

  Future<bool> showRewardedAd() async {
    if (!_isInitialized) {
      print("RewardAdService: Show called, but service not initialized.");
      return false;
    }
    if (!_shouldShowAds) {
      print('RewardAdService: Show called, but ads are disabled by remote config.');
      return false;
    }
    if (!_isRewardedAdLoaded || _rewardedAd == null) {
      print('RewardAdService: Show called, but ad not loaded or ad object is null. It should have been preloaded.');
      // We do NOT attempt to load here. If it's not ready, it's not ready.
      return false;
    }

    _hasEarnedRewardInternal = false; // Reset before showing

    try {
      print('RewardAdService: Attempting to show ad. Current ad object: $_rewardedAd');
      await _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          print('RewardAdService: User earned reward: ${reward.amount} ${reward.type}');
          _hasEarnedRewardInternal = true;
          notifyListeners();
        },
      );
      // Ad was presented. State of _rewardedAd and _isRewardedAdLoaded will be handled
      // by onAdDismissedFullScreenContent or onAdFailedToShowFullScreenContent.
      return true;
    } catch (e) {
      print('RewardAdService: ERROR showing rewarded ad: $e');
      // Aggressively clean up and try to preload for the next opportunity
      _rewardedAd?.dispose();
      _rewardedAd = null;
      _isRewardedAdLoaded = false;
      _hasEarnedRewardInternal = false;
      notifyListeners(); // Notify of this failure state
      if (_isInitialized && _shouldShowAds) loadRewardedAd(); // Attempt to get a new ad
      return false;
    }
  }

  void resetReward() {
    if (_hasEarnedRewardInternal) {
      print('RewardAdService: Resetting reward state.');
      _hasEarnedRewardInternal = false;
      // notifyListeners(); // Usually, UI doesn't need to react to this specific reset
                          // unless hasEarnedReward is directly driving something post-reward.
    }
  }

  @override
  void dispose() {
    print('RewardAdService: Disposed.');
    _rewardedAd?.dispose();
    super.dispose();
  }
}