import 'package:bhagawathgitaapp/data/data.dart';
import 'package:bhagawathgitaapp/model/future_functions.dart';
// import 'package:bhagawathgitaapp/model/sharedPreferences/custom_options.dart';
import 'package:bhagawathgitaapp/model/utils.dart';
import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:bhagawathgitaapp/screens/services/navigator_helper.dart';
// import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/constraints.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/list_view_screen.dart';
// import 'package:bhagawathgitaapp/screens/widgets/datta_appbar.dart';
// import 'package:bhagawathgitaapp/screens/widgets/datta_floatingtext.dart';
import 'package:bhagawathgitaapp/screens/widgets/datta_halfbutton.dart';
import 'package:bhagawathgitaapp/screens/widgets/menu.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class ImpSlokaScreen extends StatefulWidget {
  const ImpSlokaScreen({Key? key}) : super(key: key);

  @override
  State<ImpSlokaScreen> createState() => _ImpSlokaScreenState();
}

class _ImpSlokaScreenState extends State<ImpSlokaScreen> {
  List info = [2, 1, 8];

  List<dynamic> getScreenSpecified(context) {
    print(getScreenWidth(context));
    if (getScreenWidth(context) > 1200) {
      return [4, 2, 18];
    }
    if (getScreenWidth(context) > 900) {
      return [3, 2, 18];
    }
    if (getScreenWidth(context) > 700) {
      return [3, 1.5, 18];
    }
    if (getScreenWidth(context) > 550) {
      return [3, 1, 18];
    }
    return [2, 1, 8];
  }

  @override
  Widget build(BuildContext context) {
    info = getScreenSpecified(context);
    
    final adService = Provider.of<NavigationAdService>(context, listen: false);

    return Scaffold(
      bottomNavigationBar: BannerAdWidget(location: "banner_imp_sloka_container",) ,
        appBar: AppBar(
          toolbarHeight: 80,
          elevation: 10,
          leading: Builder(
            builder: (BuildContext context) {
              return IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () => Scaffold.of(context).openDrawer(),
              );
            },
          ),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 20,
              ),
              Text(
                "ముఖ్యమైన శ్లోకాలు",
                style: GoogleFonts.dhurjati(color: Colors.black),
              ),
              Text(
                "కోపం, శాంతి, కామం మరియు అనేక భావోద్వేగాలపై శ్లోకాలు",
                style: GoogleFonts.mallanna(color: Colors.black, fontSize: 16),
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        drawer: Menu(),
        body: CustomScrollView(
          slivers: [
            // SliverList(
            //   delegate: SliverChildListDelegate(
            //     [
            //       const DattaAppBarAsset(
            //         imagePath: "assets/krishna.png",
            //         text: "ముఖ్యమైన శ్లోకాలు",
            //         right: 0,
            //         bottom: 5,
            //         height: 220.0,
            //         heroTag: "Krishna",
            //       ),
            //       const DattaFloatingText(futureData: getImpFloatingText),
            //     ],
            //   ),
            // ),
            SliverPadding(
              padding: const EdgeInsets.all(10),
              sliver: SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: info[0],
                  crossAxisSpacing: 8.0,
                  mainAxisSpacing: info[2].toDouble(),
                  childAspectRatio: info[1].toDouble(),
                ),
                delegate: SliverChildListDelegate([
                  for (var e in impSlokaData.entries)
                    DattaHalfButtonAsset(
                        onTap: () {
                          //impSlokaFuture
                            NavigationHelper.navigateWithAd(
                        context,
                              PageRouteBuilder(
                                  pageBuilder: (context, a1, a2) =>
                                      ListViewScreen(
                                          id: e.key,
                                          title: e.key,
                                          futureData: impSlokaFuture)),
                                          adService
                                          );
                        },
                        title: e.key,
                        text: impSlokaData[e.key]["text"])
                ]),
              ),
            ),
          ],
        )
        );
  }
}
