name: bhagawa<PERSON><PERSON><PERSON>pp
description: "BhagwathGita Telugu App"
publish_to: 'none'
version: 2.0.5+17

environment:
  sdk: ^3.7.0

dependencies:
  datta_panchang:
    path: ../packages/datta_panchang
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.2.1
  just_audio: ^0.10.2
  audio_video_progress_bar: ^2.0.3
  swipe_to: ^1.0.6
  shared_preferences: ^2.2.0
  marquee: ^2.2.3
  share_plus: ^10.1.3
  youtube_player_iframe: ^5.2.0
  http: ^1.1.0
  fluttertoast: ^8.2.2
  flutter_toggle_tab: ^1.4.1
  in_app_review: ^2.0.10
  webview_flutter: ^4.7.0
  permission_handler: ^11.3.1
  geolocator: ^9.0.2
  flutter_tts: ^4.2.1
  flutter_chat_ui: ^2.1.3
  flutter_chat_core: ^2.1.2
  flyer_chat_text_message: ^2.1.2
  uuid: ^4.5.1
  firebase_core: ^3.10.1
  firebase_messaging: ^15.2.1
  flutter_local_notifications: ^17.2.1
  google_mobile_ads: ^6.0.0
  provider: ^6.1.2
  firebase_remote_config: ^5.3.1
  package_info_plus: ^8.1.3
  encrypt: ^5.0.3


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  generate: true
  uses-material-design: true
  assets:
     - assets/

flutter_native_splash:
  image: assets/splash_img.png
  color: "#ffffff"
  branding: assets/heading_img.png
  android: true
  ios: true
  web: true

  android_12:
    image: assets/splash_img.png
    color: "#ffffff"
    branding: assets/heading_img.png