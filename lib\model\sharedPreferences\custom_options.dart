import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:bhagawathgitaapp/constraints.dart';

Future<String> getAdMobBannerId() async {
  SharedPreferences prefs=await SharedPreferences.getInstance();
  return prefs.getString("adMobBannerId")??bannerUnitId;
}

Future<String> getAdMobIntestialId() async {
  SharedPreferences prefs=await SharedPreferences.getInstance();
  return prefs.getString("adMobIntestialId")??"ca-app-pub-3940256099942544/1033173712";
}

Future<String> getMainFloatingText() async {
  try{
    String url="https://airbushack-default-rtdb.firebaseio.com/mainMessage/.json";
    final response = await http.get(Uri.parse(url));

    return response.body.substring(1,response.body.length-1);
  }
  catch(e){
    return "భగవత్గీత కోసం తెలుగులో ఉత్తమ అప్లికేషన్...   ";
  }

  // SharedPreferences prefs=await SharedPreferences.getInstance();
  // return prefs.getString("mainFloatingText")??"భగవత్గీత కోసం తెలుగులో ఉత్తమ అప్లికేషన్...  ";
}

Future<String> getImpFloatingText() async {
  try{
    String url="https://airbushack-default-rtdb.firebaseio.com/impSloka/.json";
    final response = await http.get(Uri.parse(url));

    return response.body.substring(1,response.body.length-1);
  }
  catch(e){
    return "కోపం, శాంతి, కామం మరియు అనేక భావోద్వేగాలపై శ్లోకాలు ...  ";
  }
  // SharedPreferences prefs=await SharedPreferences.getInstance();
  // return prefs.getString("impFloatingText")??"కోపం, శాంతి, కామం మరియు అనేక భావోద్వేగాలపై శ్లోకాలు ...  ";
}

Future loadCustomOptions() async{
  String url="https://airbushack-default-rtdb.firebaseio.com/customOptions/.json";
  SharedPreferences prefs=await SharedPreferences.getInstance();
  final response = await http.get(Uri.parse(url));
  var data = json.decode(response.body);
  data.forEach((k,v) {
    prefs.setString(k,v);
  }); 
}