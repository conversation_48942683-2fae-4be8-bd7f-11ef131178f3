// import 'package:flutter/material.dart';
// import 'package:google_mobile_ads/google_mobile_ads.dart';

// class TpBannerAd extends StatefulWidget {
//   const TpBannerAd(this.unitId, {Key? key}) : super(key: key);

//   final String unitId;

//   @override
//   _TpBannerAdState createState() => _TpBannerAdState();
// }

// class _TpBannerAdState extends State<TpBannerAd> {
//   BannerAd? _bannerAd;
//   bool _loadAdFailed = false;

//   Future<AdWidget> initBannerAds() async {
//     AnchoredAdaptiveBannerAdSize? adaptiveSize =
//         await anchoredAdaptiveBannerAdSize(context);
//     int width = adaptiveSize?.width ?? 320;
//     int height = adaptiveSize?.height ?? 50;

//     _bannerAd = BannerAd(
//       adUnitId: widget.unitId,
//       size: AdSize(height: height, width: width),
//       request: const AdRequest(),
//       listener: BannerAdListener(onAdFailedToLoad: (Ad ad, LoadAdError err) {
//         setState(() {
//           _loadAdFailed = true;
//         });
//       }),
//     );

//     _bannerAd?.load();

//     return Future<AdWidget>.value(AdWidget(ad: _bannerAd!));
//   }

//   Future<AnchoredAdaptiveBannerAdSize?> anchoredAdaptiveBannerAdSize(
//       BuildContext context) async {
//     return await AdSize.getAnchoredAdaptiveBannerAdSize(
//       MediaQuery.of(context).orientation == Orientation.portrait
//           ? Orientation.portrait
//           : Orientation.landscape,
//       MediaQuery.of(context).size.width.toInt(),
//     );
//   }

//   @override
//   void dispose() {
//     _bannerAd?.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return !_loadAdFailed
//         ? FutureBuilder<AdWidget>(
//             future: initBannerAds(),
//             builder: (BuildContext ctx, AsyncSnapshot<AdWidget> snapshot) {
//               return snapshot.hasData
//                   ? Container(
//                       color: Colors.white,
//                       alignment: Alignment.center,
//                       child: snapshot.data,
//                       width: _bannerAd?.size.width.toDouble(),
//                       height: _bannerAd?.size.height.toDouble(),
//                     )
//                   : Container(height: 0,);
//             },
//           )
//         : Container(height: 0,);
//   }
// }

import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:provider/provider.dart';

// Updated BannerAdWidget that takes a location parameter
class BannerAdWidget extends StatelessWidget {
  final String location;
  final String? subLocation;

  const BannerAdWidget({
    Key? key,
    required this.location,
    this.subLocation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final remoteConfig = RemoteConfigService();

    bool isAdEnabled =
        remoteConfig.getValue<bool>(key: location, defaultValue: false);
    
    String finalLoaction = subLocation != null ? '$location-$subLocation' : location;
    print("guru hi ${finalLoaction} ${isAdEnabled}");


    return !isAdEnabled
        ? SizedBox()
        : Consumer<BannerAdService>(
            builder: (context, adService, child) {
              if (!adService.isAdLoaded(finalLoaction)) {
                adService.loadBannerAd(finalLoaction);
                return const SizedBox();
              }

              final bannerAd = adService.getBannerAd(finalLoaction);
              if (bannerAd == null) return const SizedBox();

              return SizedBox(
                width: bannerAd.size.width.toDouble(),
                height: bannerAd.size.height.toDouble(),
                child: AdWidget(ad: bannerAd),
              );
            },
          );
  }
}
