import 'package:bhagawathgitaapp/l10n/l10n.dart';
import 'package:bhagawathgitaapp/model/sharedPreferences/custom_options.dart';
import 'package:bhagawathgitaapp/screens/home_page.dart';
import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:bhagawathgitaapp/screens/services/notification_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:provider/provider.dart';


// flutter gen-l10n   
// flutter pub run flutter_native_splash:create    
void main() async {
  
  Locale locale = const Locale('te');

  initialize();
  await NotificationService.initialize();
  await MobileAds.instance.initialize();

  final remoteConfigService = RemoteConfigService();
  await remoteConfigService.initialize();

  final adService = BannerAdService(remoteConfigService);
  await adService.initialize();
  final navigationAdService = NavigationAdService(remoteConfigService);
  navigationAdService.initialize();
  final rewardAdService = RewardAdService(remoteConfigService);
  rewardAdService.initialize();
  

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => navigationAdService),
        ChangeNotifierProvider(create: (_) => adService),
        ChangeNotifierProvider(create: (_) => rewardAdService),
      ],
      child: MaterialApp(
      title: 'BhagwathGita Telugu',
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
      supportedLocales: L10n.all,
        locale: locale,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate
        ],
    ),
    ),
  );
}

void initialize(){
  WidgetsFlutterBinding.ensureInitialized();
  // makeAppFullScreen();
  // getGoogleAdsInitialize();
  loadCustomOptions();
}

void getGoogleAdsInitialize(){
  // MobileAds.instance.initialize(); 
}

void makeAppFullScreen(){
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.bottom]);
}