import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
import 'package:datta_panchang/panchang_at_this_moment.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';

class PanchangAtThisMomentScreen extends StatefulWidget {
  @override
  _PanchangAtThisMomentScreenState createState() =>
      _PanchangAtThisMomentScreenState();
}

class _PanchangAtThisMomentScreenState
    extends State<PanchangAtThisMomentScreen> {
  double? latitude;
  double? longitude;
  double? altitude;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchLocation();
  }

  Future<void> _fetchLocation() async {
    PermissionStatus permission = await Permission.location.request();

    if (permission.isGranted) {
      try {
        Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.low,
        );
        setState(() {
          latitude = position.latitude;
          longitude = position.longitude;
          altitude = position.altitude;
          isLoading = false;
        });
      } catch (e) {
        print("Error fetching location: $e");
        _useDefaultLocation();
      }
    } else if (permission.isDenied || permission.isPermanentlyDenied) {
      if (permission.isPermanentlyDenied) {
        await openAppSettings(); // Optional: Prompt user to open settings
      }
      print("Permission denied. Using default location values.");
      _useDefaultLocation();
    }
  }

  void _useDefaultLocation() {
    setState(() {
      latitude = 23.1823900;
      longitude = 75.7764300;
      altitude = 0.494;
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BannerAdWidget(location: "banner_PanchangAtTheMoment",) ,
      appBar: AppBar(
        elevation: 10,
        // backgroundColor: Colors.white,
        title: Text(
          "ఈ క్షణం పంచాంగం",
          style: GoogleFonts.peddana(color: Colors.black, fontSize: 22),
        ),
      ),
      body: isLoading 
        ? const Center(child: CircularProgressIndicator())
        : PanchangAtTheMoment(
            key: Key('{lat:$latitude,lng:$longitude,alt:$altitude}'),
            startTime: DateTime.now(),
            lat: latitude!,
            lng: longitude!,
            alt: altitude!,
          ),
    );
  }
}