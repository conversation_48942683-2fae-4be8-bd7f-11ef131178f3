import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  factory RemoteConfigService() => _instance;
  RemoteConfigService._internal();

  FirebaseRemoteConfig? _remoteConfig;

  // Initialize Remote Config
  Future<void> initialize() async {
    try {
      _remoteConfig = FirebaseRemoteConfig.instance;

        // "interstitial_ad_uid" : 'ca-app-pub-4786323951205927/9726670102',
        // "banner_ad_uid" : 'ca-app-pub-4786323951205927/9181258081',
        // "reward_ad_uid" : 'ca-app-pub-4786323951205927/9851708394',

      // Set default values
      await _remoteConfig?.setDefaults({
        'welcome_message': "",
        'banner_homePage': true,      
        "banner_sloka_container": true,
        "banner_imp_sloka_container": true,
        "banner_PanchangAtTheMoment": true,
        "banner_audio_player": true,
        "banner_custom_sloka_screen": true,
        "banner_listView_screen": true,
        "interstitial_ad_uid" : 'ca-app-pub-3940256099942544/**********',
        "banner_ad_uid" : 'ca-app-pub-3940256099942544/**********',
        "reward_ad_uid" : 'ca-app-pub-3940256099942544/**********',
        "show_interstitial_ads" : true,
        "show_reward_ads" : true,
        "interstitial_min_ad_interval_seconds":30,
        "interstitial_screens_between_ads":3,
        "ai_bot_key":"****************************************",
        "ai_bot_provider": "github-gpt",
        "ai_bot_temperature": 0.5,
        "min_supported_version": "1.0.0",
        "show_youtube_videos": true,
        "no_of_messages_limit": 10
        // Add more default remote config parameters here
      });

      // Set configuration settings
      await _remoteConfig?.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: kDebugMode 
          ? const Duration(minutes: 1) 
          : const Duration(minutes: 5),
      ));

      // Fetch and activate the remote config
      await fetchAndActivate();
    } catch (e) {
      print('Error initializing remote config: $e');
    }
  }

  // Fetch and activate remote config
  Future<bool> fetchAndActivate() async {
    try {
      await _remoteConfig?.fetch();
      return await _remoteConfig?.activate() ?? false;
    } catch (e) {
      print('Error fetching remote config: $e');
      return false;
    }
  }

  // Generic method to get various types of remote config values
  T getValue<T>({required String key, T? defaultValue}) {
    if (_remoteConfig == null) {
      print('Remote Config not initialized');
      return defaultValue as T;
    }

    try {
      if (T == bool) {
        return _remoteConfig!.getBool(key) as T;
      } else if (T == String) {
        return _remoteConfig!.getString(key) as T;
      } else if (T == int) {
        return _remoteConfig!.getInt(key) as T;
      } else if (T == double) {
        return _remoteConfig!.getDouble(key) as T;
      } else {
        print('Unsupported type for remote config');
        return defaultValue as T;
      }
    } catch (e) {
      print('Error getting remote config value for $key: $e');
      return defaultValue as T;
    }
  }

  // Example specific getters
  bool get isFeatureEnabled => 
    getValue(key: 'feature_flag_example', defaultValue: false);

  String get welcomeMessage => 
    getValue(key: 'welcome_message', defaultValue: 'Welcome!');

  bool get isAppUpdateRequired => 
    getValue(key: 'app_update_required', defaultValue: false);
}