// import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/constraints.dart';
import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
import 'package:bhagawathgitaapp/screens/ads/google_ad_service.dart';
import 'package:flutter/material.dart';
import 'package:bhagawathgitaapp/model/data_objects.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:bhagawathgitaapp/screens/widgets/audio_player.dart';
import 'package:provider/provider.dart';

class AudioScreen extends StatefulWidget {
  final List<SongInfo> songs;
  final int index;
  const AudioScreen({
     Key? key,
     required this.songs,
     required this.index,
  }) : super(key: key);

  @override
  State<AudioScreen> createState() => _AudioScreenState();
}

class _AudioScreenState extends State<AudioScreen> {
  int index=0;

  @override
  void initState(){
    super.initState();
    index=widget.index;
  }

  AppBar appBar(context){
    return AppBar(
        elevation: 10,
        backgroundColor: Colors.white,
        title: const Center(child: Text("Audio Player",style: TextStyle(color: Colors.black),)),
        leading: IconButton(onPressed: (){
          Navigator.pop(context);
        },icon: const Icon(Icons.arrow_back,color: Colors.black,)),
        );
  }

  leftSkip() async {
    final adService = Provider.of<NavigationAdService>(context, listen: false);
    await adService.handleContentUpdate();

    setState(() {
      index-=1;
    });
  }

  rightSkip() async{
    
    final adService = Provider.of<NavigationAdService>(context, listen: false);
    await adService.handleContentUpdate();
    setState(() {
      index+=1;
    });
  }

  onComplete() async {
    
    final adService = Provider.of<NavigationAdService>(context, listen: false);
    await adService.handleContentUpdate();

    setState(() {
      index+=1;
    });
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(context),
      bottomNavigationBar: BannerAdWidget(location: "banner_audio_player",) ,
      body: ListView(
        children: [
          const SizedBox(height: 20,),
          Image.asset("assets/krishna.png",height: MediaQuery.of(context).size.height/2,),
          const SizedBox(height: 25,),
          Center(
            child: Text(widget.songs[index].name,style: GoogleFonts.mallanna(fontSize: 25,color: Colors.blue),)),
          const SizedBox(height: 25,),
          AudioplayerWidget(
            url:widget.songs[index].url,
            down: true,
            leftSwipe: (index!=0)?leftSkip:null,
            rightSwipe: (index!=(widget.songs.length-1))?rightSkip:null,
            onComplete: (index!=(widget.songs.length-1))?onComplete:null,
            autoPlay : true
            )
        ],
      ),
      //bottomNavigationBar: const TpBannerAd(bannerUnitId),
      );
  }
}