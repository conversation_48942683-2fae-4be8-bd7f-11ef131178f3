import 'package:bhagawathgitaapp/model/sloka_rotation.dart';
import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/screens/ads/banner_ads.dart';
// import 'package:bhagawathgitaapp/constraints.dart';
import 'package:bhagawathgitaapp/screens/widgets/sloka_appbar.dart';
import 'package:bhagawathgitaapp/screens/widgets/sloka_container.dart';
import 'package:bhagawathgitaapp/screens/widgets/sloka_custom_popup.dart';
import 'package:flutter/material.dart';
import 'package:swipe_to/swipe_to.dart';

class CustomSlokaPage extends StatefulWidget {
  final String title;
  final String sloka;
  final List<String> slokas;

  const CustomSlokaPage(
      {Key? key,
      required this.title,
      required this.sloka,
      required this.slokas})
      : super(key: key);

  @override
  _CustomSlokaPageState createState() => _CustomSlokaPageState();
}

class _CustomSlokaPageState extends State<CustomSlokaPage> {
  int _index = 0;
  bool autoPlay = false;

  @override
  void initState() {
    _index = widget.slokas.indexOf(widget.sloka);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final List<String> slokas = widget.slokas;
    final int chapterNo = int.parse(slokas[_index].split(":")[0]);
    final int slokaNo = int.parse(slokas[_index].split(":")[1]);
    return Scaffold(
      backgroundColor: Colors.white,
      bottomNavigationBar: BannerAdWidget(location: "banner_custom_sloka_screen",) ,
      appBar: SlokaAppbar(
        chapterNo: chapterNo,
        slokaNo: slokaNo,
        title: widget.title,
        leftAction: _leftSwipeCustom,
        rightAction: _rightSwipeCustom,
      ),
      body: SwipeTo(
        onRightSwipe: (d) => _leftSwipeCustom(),
        onLeftSwipe: (d) => _rightSwipeCustom(),
        child: SlokaContainer(
          chapterNo: chapterNo,
          slokaNo: slokaNo,
          onSlokaAudioCompleted: (bool updateAutoPlay) {
            setState(() {
              autoPlay = updateAutoPlay;
            });
            _rightSwipeCustom();
          },
          autoPlaySloka: autoPlay,
          popupAction: () {
            changeCustomSloka(
              context,
              slokas[_index],
              slokas,
              widget.title,
              _popupAction,
            );
          },
        ),
      ),
    );
  }

  void _leftSwipeCustom() {
    setState(() {
    _index = leftRotateCustom(_index, widget.slokas.length);
    });
  }

  void _rightSwipeCustom() {
    setState(() {
       _index = rightRotateCustom(_index, widget.slokas.length);
    });
  }

  void _popupAction(String val) {
    setState(() {
      _index = widget.slokas.indexOf(val);
    });
  }
}
