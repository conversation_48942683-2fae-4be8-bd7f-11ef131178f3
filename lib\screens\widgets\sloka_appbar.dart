import 'package:bhagawathgitaapp/screens/widgets/favorite_button.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SlokaAppbar extends StatelessWidget implements PreferredSizeWidget{
  final String title;
  final int chapterNo,slokaNo;
  final Function() leftAction;
  final Function() rightAction;
  const SlokaAppbar({ 
    Key? key,
    required this.title,
    required this.chapterNo,
    required this.slokaNo,
    required this.leftAction,
    required this.rightAction
   }) : super(key: key);

  @override
  Size get preferredSize => Size.fromHeight(AppBar().preferredSize.height);
  
  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 3,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.grey,
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Colors.black,
        ),
        onPressed: (){leftAction();},
      ),
      title: Center(child:Text(title,
        style: GoogleFonts.peddana(color: Colors.black,fontSize: 20),)),
      actions: [
        FavoriteButton(chapterNo: chapterNo,slokaNo:slokaNo),
        IconButton(
        icon: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.black,
        ),
        onPressed: (){rightAction();},
      ),
      ],
      );
  }
}