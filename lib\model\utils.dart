import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

Color listToColor(List? l){
  if (l==null){
    return Colors.white;
  }
  return Color.fromRGBO(l[0].toInt(), l[1].toInt(), l[2].toInt(), l[3].toDouble());
}

double getScreenWidth(context){
  return MediaQuery.of(context).size.width;
}

double getScreenHeight(context){
  return MediaQuery.of(context).size.height;
}

void shareIt(String text){
  String apInfo="Download bhagwathgita telugu app from playstore: \nhttps://play.google.com/store/apps/details?id=com.bhagawathgita.telugu";
  text=text+"\n\n"+apInfo;
  Share.share(text);
}