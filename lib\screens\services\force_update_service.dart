import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ForceUpdateService {
  static Future<void> checkForUpdate(BuildContext context) async {
    final remoteConfig = FirebaseRemoteConfig.instance;
    
    // Fetch the latest version from Remote Config
    await remoteConfig.fetchAndActivate();
    
    final minSupportedVersion = remoteConfig.getString('min_supported_version');
    
    // Get current app version
    final packageInfo = await PackageInfo.fromPlatform();
    final currentVersion = packageInfo.version;

    // Compare versions
    if (_isUpdateRequired(currentVersion, minSupportedVersion)) {
      await _showUpdateDialog(context);
    }
  }

  static bool _isUpdateRequired(
    String currentVersion, 
    String minSupportedVersion
  ) {
    // Implement version comparison logic
    // print("guru ${currentVersion} ${minSupportedVersion}");
    return _compareVersions(currentVersion, minSupportedVersion) < 0;
  }

  static Future<void> _showUpdateDialog(
    BuildContext context
  ) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope (
          canPop:  false,
          child: AlertDialog(
            title: Text('Update Required'),
            content: Text('A new version of app is available. Please update to continue using the app.'),
            actions: <Widget>[
              TextButton(
                child: Text('Update Now'),
                onPressed: () {
                  // Replace with your app's store URL
                   launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu'));
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Simple version comparison (you might want to improve this)
  static int _compareVersions(String current, String required) {
    List<int> currentParts = current.split('.').map(int.parse).toList();
    List<int> requiredParts = required.split('.').map(int.parse).toList();

    for (int i = 0; i < currentParts.length; i++) {
      if (currentParts[i] < requiredParts[i]) return -1;
      if (currentParts[i] > requiredParts[i]) return 1;
    }
    return 0;
  }
}
