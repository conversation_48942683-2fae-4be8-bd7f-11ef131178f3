import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class CreditsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Credits & About'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About the App',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'I started this Bhagwathgita app in 2022 with the goal of clustering Bhagwathgita slokas by emotions. Over time, it has evolved to include features like <PERSON>chang, AI Guru, and more.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 20),
            Text(
              'Credits',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON><PERSON>(height: 10),
            Text(
              'I would like to give credit to the following individuals for their contributions:',
              style: Text<PERSON>tyle(fontSize: 16),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 10),
            ListTile(
              leading: Icon(Icons.person),
              title: Text('<PERSON> <PERSON>rahmananda'),
              subtitle: Text('For using his sloka-wise audios on Mola Sloka'),
            ),
            ListTile(
              leading: Icon(Icons.person),
              title: Text('Gantasala'),
              subtitle: Text('For using his Sampurna Bhagwathgita audio'),
            ),
            ListTile(
              leading: Icon(Icons.person),
              title: Text('ఆచంట వెంకట రామ చైతన్య కిషన్'),
              subtitle: Text('For his work in correcting the quotes'),
            ),
            ListTile(
              leading: Icon(Icons.person),
              title: Text('Prof Kota Siva Shankar Rao (Prabhuji)'),
              subtitle: Text('For sharing his 108 Important Slokas'),
            ),
            ListTile(
              leading: Icon(Icons.person),
              title: Text('A Divya'),
              subtitle: Text('For helping in creating indexes for important slokas'),
            ),
            SizedBox(height: 10),
            Text(
              'Source for Clustered Slokas on Emotions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            InkWell(
              onTap: () {
                launchURL('https://qr.ae/pYZnwv');
              },
              child: Text(
                'https://qr.ae/pYZnwv',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
            SizedBox(height: 20),
            Text(
              'Developer',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'This app was developed by Akula Datta. You can find more about me on my profile:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 10),
            InkWell(
              onTap: () {
                launchURL('https://akuladatta.github.io');
              },
              child: Text(
                'https://akuladatta.github.io',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
            SizedBox(height: 20),
            Text(
              'Copyright Issues',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'If you have any copyright issues, please feel free to contact me at:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 10),
            InkWell(
              onTap: () {
                launchEmail('<EMAIL>');
              },
              child: Text(
                '<EMAIL>',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void launchURL(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  }

  void launchEmail(String email) async {
    final Uri params = Uri(
      scheme: 'mailto',
      path: email,
    );
    String url = params.toString();
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  }
}