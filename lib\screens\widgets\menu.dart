import 'package:bhagawathgitaapp/model/future_functions.dart';
import 'package:bhagawathgitaapp/screens/creditsScreen.dart';
import 'package:bhagawathgitaapp/screens/mainScreens/panchang_at_this_moment_screen.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/list_view_screen.dart';
import 'package:bhagawathgitaapp/screens/webview.dart';
import 'package:bhagawathgitaapp/screens/widgets/today_quote.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class Menu extends StatelessWidget {
  final InAppReview inAppReview = InAppReview.instance;

  Menu({
    super.key,
  });

  Future<void> _rateApp() async {
    if (await inAppReview.isAvailable()) {
      inAppReview.openStoreListing(); // FIXME:  inAppReview.requestReview();
    } else {
      inAppReview.openStoreListing();
    }
  }

  void shareApp() {
    String appLink =
        'https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu&hl=en';
    String shareText = '''
📖 భగవద్గీత తెలుగు (App)
===============================

ఈ యాప్‌లో మీరు
- అన్ని అధ్యాయాలలోని శ్లోకాలను సులభంగా చదవచ్చు📜
- భావోద్వేగాల ప్రకారం శ్లోకాలు 😌
- భగవద్గీత ఆడియోలు 🎧
- పంచాంగం 🗓️
- మరియు భగవద్గీత ప్రకారం మీ సందేహాలకు సమాధానం చెప్పే AI గురువు 🤖
ఇలా ఎన్నో విశేషాలు ఇందులో ఉన్నాయి.

📲 డౌన్లోడ్ చేసుకోడానికి ఈ లింక్ నొక్కండి:
👉 $appLink
  ''';

    Share.share(shareText, subject: 'భగవద్గీత తెలుగు యాప్');
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          Container(
            height: 50,
          ),

          Container(
            width: 100,
            height: 100,
            margin: const EdgeInsets.only(top: 20, bottom: 20),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: Image.asset("assets/krishna_radha2.png"),
          ),

          // Features Group
          Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 4.0),
            child: Text(
              "Features",
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.grey[800],
              ),
            ),
          ),

          ListTile(
            leading: const Icon(Icons.temple_hindu, color: Colors.purple),
            title: Text(
              "Panchang at this moment",
              style: GoogleFonts.poppins(),
            ),
            onTap: () {
               Navigator.push(
                  context,
                  PageRouteBuilder(
                      pageBuilder: (context, a1, a2) =>
                          PanchangAtThisMomentScreen()));
              },
          ),
          ListTile(
            leading: const Icon(Icons.favorite, color: Colors.red),
            title: Text(
              "Favorite Slokas",
              style: GoogleFonts.poppins(),
            ),
            onTap: () {
              Navigator.push(
                  context,
                  PageRouteBuilder(
                      pageBuilder: (context, a1, a2) => const ListViewScreen(
                          title: "మీకు ఇష్టమైన శ్లోకాలు",
                          futureData: favSlokaFuture)));
              },
          ),
          ListTile(
            leading: const Icon(Icons.star, color: Colors.amber),
            title: Text(
              "Quote of the day",
              style: GoogleFonts.poppins(),
            ),
            onTap: () {
              Navigator.pop(context);
                showModalBottomSheet(
                  context: context,
                  builder: (context) => TodayQuoteWidget(),
                );
              },
          ),

          const Divider(),

          // Community Group
          Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 4.0),
            child: Text(
              "Community",
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.grey[800],
              ),
            ),
          ),

          ListTile(
            leading: const Icon(Icons.telegram, color: Colors.blue),
            title: Text(
              "Join our Telegram group",
              style: GoogleFonts.poppins(),
            ),
            onTap: () {
              launchUrl(Uri.parse('https://t.me/+y-IAi1NhA3g0NThl'));
             },
          ),
          ListTile(
            leading: const Icon(Icons.share, color: Colors.green),
            title: Text(
              "Share this app",
              style: GoogleFonts.poppins(),
            ),
            onTap: () {
              shareApp();
             },
          ),

          const Divider(),

          // Support Group
          Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 4.0),
            child: Text(
              "Support",
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.grey[800],
              ),
            ),
          ),

          ListTile(
            leading: const Icon(Icons.rate_review, color: Colors.orange),
            title: Text(
              "Rate this app",
              style: GoogleFonts.poppins(),
            ),
            onTap: () async {
              _rateApp();
            },
          ),
          ListTile(
            leading: const Icon(Icons.developer_mode, color: Colors.deepOrangeAccent),
            title: Text(
              "About Developer",
              style: GoogleFonts.poppins(
                color: Colors.deepOrangeAccent,
                fontWeight: FontWeight.w500
              ),
            ),
            onTap: () async {
             Navigator.push(context,
             PageRouteBuilder(
               pageBuilder: (context, a1, a2) => const CustomWebViewScreen(
                 appBarTitle: "Akula Guru Datta",
                 url: "https://akuladatta.github.io/",
               ),
             ));
            },
          ),

          // Legal and Information (with lighter styling)
          const Divider(),

          Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 4.0),
            child: Text(
              "Legal & Information",
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.grey[800],
              ),
            ),
          ),

          ListTile(
            leading: const Icon(Icons.privacy_tip, color: Colors.grey),
            title: Text(
              "Privacy policy",
              style: GoogleFonts.poppins(
                color: Colors.grey[700],
                fontSize: 13,
              ),
            ),
            onTap: () {
              const url = "https://garudadevdataservices.github.io/privacy_policies/bhagawathGita-privacy.html" ;
              Navigator.push(context,
              PageRouteBuilder(
                pageBuilder: (context, a1, a2) => const CustomWebViewScreen(
                  appBarTitle: "Privacy Policy",
                  url: url,
                ),
              ));
              },
          ),
          ListTile(
            leading: const Icon(Icons.description, color: Colors.grey),
            title: Text(
              "Terms and conditions",
              style: GoogleFonts.poppins(
                color: Colors.grey[700],
                fontSize: 13,
              ),
            ),
            onTap: () {
              const url = "https://garudadevdataservices.github.io/privacy_policies/bhagwathGita-terms.html" ;
              Navigator.push(context,
              PageRouteBuilder(
                pageBuilder: (context, a1, a2) => const CustomWebViewScreen(
                  appBarTitle: "Terms and conditions",
                  url: url,
                ),
              ));
              },
          ),
          ListTile(
            leading: const Icon(Icons.info, color: Colors.grey),
            title: Text(
              "Credits & About",
              style: GoogleFonts.poppins(
                color: Colors.grey[700],
                fontSize: 13,
              ),
            ),
            onTap: () {
              Navigator.push(
                  context,
                  PageRouteBuilder(
                      pageBuilder: (context, a1, a2) => CreditsScreen()
                  ));
              },
          ),
        ],
      ),
    );
  }
}
