import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CustomWebViewScreen extends StatefulWidget {
  final String url;
  final String appBarTitle;

  const CustomWebViewScreen({
    Key? key,
    required this.url,
    required this.appBarTitle,
  }) : super(key: key);

  @override
  State<CustomWebViewScreen> createState() => _CustomWebViewScreenState();
}

class _CustomWebViewScreenState extends State<CustomWebViewScreen> {
  late final WebViewController _controller;
  bool _isLoading = true; // Track loading state

  @override
  void initState() {
    super.initState();

    // Initialize the WebViewController
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (url) {
          setState(() {
            _isLoading = true; // Show loading indicator
          });
        },
        onPageFinished: (url) {
          setState(() {
            _isLoading = false; // Hide loading indicator
          });
        },
      ))
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.appBarTitle),
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller), // WebView rendering
          if (_isLoading) // Show loading indicator if loading
            Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
