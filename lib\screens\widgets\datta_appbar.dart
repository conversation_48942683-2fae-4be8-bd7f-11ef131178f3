import 'package:bhagawathgitaapp/model/utils.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DattaAppBarAsset extends StatelessWidget {
  // background
  final List? backgroundColor1;
  final List? backgroundColor2;
  final String? backgroundImage;
  final double? height;
  // image properties
  final String? imagePath;
  final double? right;
  final double? bottom;
  final double? width;
  // text properties
  final String? text;
  final double? fontSize;
  final double? boarderRadius;
  final List? fontColorRGBA;
  final String? heroTag;

  const DattaAppBarAsset(
      {Key? key,
      this.backgroundColor1 = const [0, 255, 255, 1],
      this.backgroundColor2 = const [153, 255, 51, 0.5],
      this.backgroundImage,
      this.height=245,
      required this.imagePath,
      required this.text,
      this.right=0,
      this.bottom=0,
      this.width = 200,
      this.fontSize=28,
      this.boarderRadius=0.5,
      this.fontColorRGBA=const [255, 140, 0, 1],
      this.heroTag})
      : super(key: key);

  BoxDecoration background() {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        colors: [
          listToColor(backgroundColor1),
          listToColor(backgroundColor2),
        ],
      ),
      image: const DecorationImage(
        image: AssetImage("assets/d-virus.png"),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: MyClipper(),
      child: Container(
          padding: const EdgeInsets.only(left: 5, top: 15, right: 0),
          height: height,
          width: double.infinity,
          decoration: background(),
          child: Row(
            children: [imageBar(context), textBar(context)],
          )),
    );
  }

  imageBar(context) {
    return SizedBox(
      width: getScreenWidth(context) / 2 - 5,
      child: Stack(
          children: [
            Positioned(
              bottom: bottom,
              right: right,
              child: (heroTag==null)?Image.asset(imagePath!, width: width):
                        Hero(tag: heroTag!,child: Image.asset(imagePath!, width: width),))
            ]),
    );
  }

  textBar(context) {
    return SizedBox(
      width: getScreenWidth(context) / 2,
      child:Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(child:Text(
              text!,
              style: GoogleFonts.suranna(
                shadows:[
                        Shadow(
                          offset: const Offset(1.0, 1.0),
                          blurRadius: boarderRadius!,
                          color: const Color.fromARGB(255, 0, 0, 0),
                        )
                      ],
                fontWeight: FontWeight.bold,
                fontSize: fontSize,
                height: 1.3,
                color: listToColor(fontColorRGBA)),
            )),
        ],
      ),
    );
  }
}

class MyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - 40);
    path.quadraticBezierTo(
        size.width / 2, size.height + 30, size.width, size.height - 40);
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}
