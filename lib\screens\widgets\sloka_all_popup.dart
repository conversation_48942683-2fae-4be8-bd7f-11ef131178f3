import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';

popupDialog(context,chapterNo,slokaNo,Function(String,String) updateFunction){
  List<String> chapters = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18'];
  Map slokas={'1': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47'], '2': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72'], '3': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43'], '4': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42'], '5': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29'], '6': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47'], '7': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30'], '8': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28'], '9': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34'], '10': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42'], '11': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55'], '12': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'], '13': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34'], '14': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27'], '15': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'], '16': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24'], '17': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28'], '18': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78']};
  showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            backgroundColor: Colors.white,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20.0))),
            content: SlokaDropDown(chapters: chapters, slokas: slokas, chapterNo: chapterNo, slokaNo: slokaNo, updateFunction: updateFunction),
          );
        });});
}

class SlokaDropDown extends StatefulWidget {

  final List<String> chapters;
  final Map slokas;
  final String chapterNo;
  final String slokaNo;
  final Function(String,String) updateFunction;

  const SlokaDropDown({
    Key? key,
    required this.chapterNo,
    required this.slokaNo,
    required this.chapters,
    required this.slokas,
    required this.updateFunction
  }) : super(key: key);

  @override
  State<SlokaDropDown> createState() => _SlokaDropDownState();
}

class _SlokaDropDownState extends State<SlokaDropDown> {

  late String chapterNo;
  late String slokaNo;

  @override
  void initState() {
    chapterNo=widget.chapterNo;
    slokaNo=widget.slokaNo;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        // _buildTitle(),
        _buildDropdowns(),
        _buildActionButton(context),
      ],
    ),
  );
}

// Widget _buildTitle() {
//   return Padding(
//     padding: const EdgeInsets.all(8.0),
//     child: Text(
//       "అధ్యాయము స్లోకము ఎంచుకోండి",
//       style: GoogleFonts.mallanna(
//         color: Colors.red,
//         fontSize: 20,
//         fontWeight: FontWeight.w500,
//       ),
//     ),
//   );
// }


Widget _buildDropdowns() {
  return Padding(
    padding: const EdgeInsets.all(0.0),
    child: Column(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Row(
          children: [
            SizedBox(width: 80,child: Text('అధ్యాయము  ', style: TextStyle(color: Colors.blue))),
            _buildDropdown(
              value: chapterNo,
              hint: 'Select chapterNo',
              items: widget.chapters,
              onChanged: (newValue) => setState(() => chapterNo = newValue ?? ''),
            ),
          ],
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          children: [
             SizedBox(width: 80,child:Text('శ్లోకం  ', style: TextStyle(color: Colors.blue))),
            _buildDropdown(
              value: slokaNo,
              hint: 'Select Month',
              items: widget.slokas[chapterNo],
              onChanged: (newValue) => setState(() => slokaNo = newValue ?? ''),
            ),
          ],
        ),
      ],
    ),
  );
}

Widget _buildDropdown({
  required String value,
  required String hint,
  required List<String> items,
  required ValueChanged<String?> onChanged,
}) {
  return Expanded(
    child: DropdownButtonFormField<String>(
      decoration: InputDecoration(
        // contentPadding: const EdgeInsets.symmetric(horizontal: 10),
        filled: true,
        fillColor: Colors.white,
        hintText: hint,
        hintStyle: const TextStyle(fontWeight: FontWeight.normal),
        border: OutlineInputBorder(
          // borderSide: const BorderSide(color: Colors.amber),
          borderRadius: BorderRadius.circular(5),
        ),
      ),
      value: value,
      icon: const Icon(Icons.arrow_drop_down),
      iconSize: 24,
      elevation: 16,
      style: const TextStyle(color: Colors.black),
      onChanged: onChanged,
      items: items.map((String option) {
        return DropdownMenuItem(
          value: option,
          child: Text(option),
        );
      }).toList(),
    ),
  );
}

Widget _buildActionButton(BuildContext context) {
  return Container(
    padding: const EdgeInsets.only(top: 20),
    width: double.infinity,
    child: TextButton(
      onPressed: () {
        widget.updateFunction(chapterNo, slokaNo);
        Navigator.of(context).pop();
      },
      child: const Text(
        'OK',
        style: TextStyle(color: Colors.green),
      ),
      style: TextButton.styleFrom(
        backgroundColor: Colors.yellow,
      ),
    ),
  );
}

}
