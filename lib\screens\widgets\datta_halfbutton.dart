import 'package:bhagawathgitaapp/model/utils.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DattaHalfButtonAsset extends StatelessWidget {
  final double? height;
  final Function() onTap;
  final String title;
  final String text;
  final double? width;
  final double? imageHeight;
  final String? imagePath;
  final String? heroTag;
  final bool hideImageSpace;

  DattaHalfButtonAsset({ 
    Key? key,
    this.height=200,
    required this.onTap,
    required this.title,
    required this.text,
    this.width,
    this.imageHeight=80,
    this.imagePath,
    this.heroTag,
    this.hideImageSpace=false
   }) : super(key: key);

  BoxDecoration decoration(){
    return BoxDecoration(
            color: Colors.transparent,
            boxShadow: [
              BoxShadow(
                offset: const Offset(0, 8),
                blurRadius: 24,
                color: const Color(0xFFB7B7B7).withValues(alpha:.16),
              )
            ],
          );
  }

  double getWidth(context){
    if (this.width==null){
      return getScreenWidth(context);
    }
    return this.width!;
  }

  ClipRRect textArea(context){
    return ClipRRect(
      borderRadius: BorderRadius.circular(25.0),
      child: Container(
          color: Colors.white,
          height: height,
          padding: const EdgeInsets.only(left: 10, right: 5),
          width: getWidth(context)/2-20,
          child: Column(children: [
            Container(
              height: height!/4,
            ),
            Text(
              title,
              style: GoogleFonts.dhurjati(
                fontWeight: FontWeight.w200,
                fontSize: 20,
                height: 1.3,
              ),
            ),
            const Spacer(),
            Text(
              text,
              style: GoogleFonts.mallanna(
                fontSize: 15,
                height: 1.3,
              ),
            ),
            const Spacer(),
            const Align(
                alignment: Alignment.bottomRight,
                // child: Icon(Icons.arrow_forward_ios)
                ),
          ])),
    );
  }
  Widget image(){
    return hideImageSpace?Container(): Image.asset(
                  imagePath!,
                  height: imageHeight,
                  fit: BoxFit.contain,
                  alignment: Alignment.centerLeft,
                );
  }

  SizedBox imageArea(context){
    return SizedBox(
              width: getWidth(context)/ 3,
                child: (heroTag==null)?image():Hero(tag: heroTag!, child: image())
            );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: decoration(),
      height: height!+40,
      width: getWidth(context)/2-20,
      child: InkWell(
        onTap: onTap,
        child: (imagePath==null && !hideImageSpace)?textArea(context):
                Stack(children: [
                  Positioned(top:30,child: textArea(context)),
                  imageArea(context)
                ],)
        ),
    );
  }
}