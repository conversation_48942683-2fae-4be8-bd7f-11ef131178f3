import 'package:bhagawathgitaapp/model/future_functions.dart';
import 'package:bhagawathgitaapp/screens/mainScreens/panchang_at_this_moment_screen.dart';
import 'package:bhagawathgitaapp/screens/services/firebase_remote_config.dart';
import 'package:bhagawathgitaapp/screens/sideScreens/list_view_screen.dart';
import 'package:bhagawathgitaapp/screens/widgets/today_quote.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class AdditionalOptions extends StatelessWidget {
  AdditionalOptions({
    Key? key,
  }) : super(key: key);

  final InAppReview inAppReview = InAppReview.instance;
  final remoteConfig = RemoteConfigService();

  Future<void> _rateApp() async {
    if (await inAppReview.isAvailable()) {
      inAppReview.openStoreListing();
    } else {
      inAppReview.openStoreListing();
    }
  }

  void shareApp() {
    String appLink =
        'https://play.google.com/store/apps/details?id=com.bhagawathgita.telugu&hl=en';
    String shareText = '''
📖 భగవద్గీత తెలుగు (App)
================================

ఈ యాప్‌లో మీరు 
- అన్ని అధ్యాయాలలోని శ్లోకాలను సులభంగా చదవచ్చు📜
- భావోద్వేగాల ప్రకారం భగవద్గీత శ్లోకాలు 😌
- భగవద్గీత ఆడియోలు 🎧
- పంచాంగం 🗓️
- మరియు భగవద్గీత ప్రకారం మీ సందేహాలకు సమాధానం చెప్పే AI గురువు 🤖 
ఇలా ఎన్నో విశేషాలు ఇందులో ఉన్నాయి.  

📲 డౌన్లోడ్ చేసుకోడానికి ఈ లింక్ నొక్కండి:
👉 $appLink
  ''';

    Share.share(shareText, subject: 'భగవద్గీత తెలుగు యాప్');
  }

  @override
  Widget build(BuildContext context) {
    bool show_youtube_videos = remoteConfig.getValue<bool>(key: 'show_youtube_videos');

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "ఇతర ఆప్షన్లు",
                    style: GoogleFonts.peddana(
                      color: Colors.black,
                      fontSize: 22,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            SingleAdditionalOption(
              buttonText: "ఈ క్షణం పంచాంగం",
              buttonIcon: Icon(
                Icons.temple_hindu_sharp,
                color: Colors.orange,
                size: 25,
              ),
              onTap: () =>
                  Navigator.push(
                          context,
                          PageRouteBuilder(
                              pageBuilder: (context, a1, a2) =>
                                  PanchangAtThisMomentScreen()))
            ),
            const SizedBox(height: 10),
            SingleAdditionalOption(
              buttonText: "మీకు ఇష్టమైన శ్లోకాలు",
              buttonIcon: Icon(
                Icons.favorite,
                color: Colors.red,
                size: 25,
              ),
              onTap: () => Navigator.pushReplacement(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, a1, a2) => const ListViewScreen(
                    title: "మీకు ఇష్టమైన శ్లోకాలు",
                    futureData: favSlokaFuture,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
            if (show_youtube_videos) SingleAdditionalOption(
              buttonText: "భగవత్గీత వీడియోలు",
              buttonIcon: Icon(
                Icons.video_library,
                color: Colors.purple,
                size: 25,
              ),
              onTap: () => Navigator.pushReplacement(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, a1, a2) => const ListViewScreen(
                    title: "భగవద్గిత వీడియోస్",
                    futureData: getVideos,
                    id: "https://airbushack-default-rtdb.firebaseio.com/gitaVideo/.json",
                    haveIntersitialAd: true,
                  ),
                ),
              ),
            ),
            if (show_youtube_videos) const SizedBox(height: 10),
            SingleAdditionalOption(
              buttonText: "ఈ రోజు మంచి మాట",
              buttonIcon: Icon(
                Icons.star,
                color: Colors.green,
                size: 25,
              ),
              onTap: () {
                Navigator.pop(context);
                showModalBottomSheet(
                  context: context,
                  builder: (context) => TodayQuoteWidget(),
                );
              },
            ),
            const SizedBox(height: 10),
            SingleAdditionalOption(
              buttonText: "Telegram లో చేరండి",
              buttonIcon: Icon(
                Icons.telegram,
                color: Colors.blue,
                size: 25,
              ),
              onTap: () =>
                  launchUrl(Uri.parse('https://t.me/+y-IAi1NhA3g0NThl')),
            ),
            const SizedBox(height: 10),
            SingleAdditionalOption(
              buttonText: "Share Our app",
              buttonIcon: Icon(
                Icons.share,
                color: Colors.red,
                size: 25,
              ),
              onTap: () async {
                shareApp();
              },
            ),
            const SizedBox(height: 10),
            SingleAdditionalOption(
              buttonText: "Rate Our App",
              buttonIcon: Icon(
                Icons.rate_review,
                color: Colors.amber,
                size: 25,
              ),
              onTap: () async {
                _rateApp();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

class SingleAdditionalOption extends StatelessWidget {
  SingleAdditionalOption({
    Key? key,
    required this.buttonText,
    required this.buttonIcon,
    required this.onTap,
  }) : super(key: key);

  final String buttonText;
  final Widget buttonIcon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.black12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha:0.5),
              spreadRadius: 1,
              blurRadius: 5,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              buttonText,
              style: GoogleFonts.mallanna(color: Colors.black, fontSize: 18),
            ),
            buttonIcon,
          ],
        ),
      ),
    );
  }
}
