import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AudioplayerWidget extends StatefulWidget {
  final String url;
  final bool down;
  final bool autoPlay;
  final Function(bool)? onCompleteForSmallWidget;
  final Function()? leftSwipe;
  final Function()? rightSwipe;
  final Function()? onComplete;

  const AudioplayerWidget(
      {Key? key,
      required this.url,
      this.down = false,
      this.leftSwipe,
      this.rightSwipe,
      this.onComplete,
      this.onCompleteForSmallWidget,
      this.autoPlay = false})
      : super(key: key);
  @override
  _AudioplayerWidgetState createState() => _AudioplayerWidgetState();
}

class _AudioplayerWidgetState extends State<AudioplayerWidget> {
  late AudioPlayer _player;
  late Stream<DurationState> _durationState;
  final _labelLocation = TimeLabelLocation.below;
  final _labelType = TimeLabelType.totalTime;
  bool _autoPlayNext = false;

  _loadAutoPlayNext() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _autoPlayNext = (prefs.getBool('autoPlayNext') ?? false);
    });
  }

  @override
  void initState() {
    super.initState();
    _loadAutoPlayNext();
    _player = AudioPlayer();
    _durationState = Rx.combineLatest2<Duration, PlaybackEvent, DurationState>(
        _player.positionStream,
        _player.playbackEventStream,
        (position, playbackEvent) => DurationState(
              progress: position,
              buffered: playbackEvent.bufferedPosition,
              total: playbackEvent.duration,
            ));
    _player.playerStateStream.listen((playerState) {
      if (playerState.processingState == ProcessingState.completed) {
        if (widget.onComplete != null) widget.onComplete!();
        if (widget.onCompleteForSmallWidget != null) {
          if (_autoPlayNext) widget.onCompleteForSmallWidget!(_autoPlayNext);
        }
        ;
      }
    });
    _init();
  }

  Future<void> _init() async {
    try {
      await _player.setUrl(widget.url);
      if (widget.autoPlay) {
        _player.play();
      } else {
        _player.pause();
      }
    } catch (e) {
      _player.pause();
    }
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  Container bottomBar() {
    return Container(
      padding: EdgeInsets.only(top: 15, left: 15, right: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(Icons.skip_previous, size: 35),
            onPressed: widget.leftSwipe,
          ),
          Container(),
          backForwardButton(),
          _playButton(),
          frontForwardButton(),
          Container(),
          IconButton(
            icon: Icon(Icons.skip_next, size: 35),
            onPressed: widget.rightSwipe,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.onCompleteForSmallWidget == null) {
      _init();
    }
    return Column(
      children: [
        Row(children: [
          if (!widget.down) _playButton(),
          Expanded(
              child: Container(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: _progressBar(),
          )),
          if (widget.onCompleteForSmallWidget != null) AutoPlayOptionButton(),
        ]),
        if (widget.down) bottomBar()
      ],
    );
  }

  Widget AutoPlayOptionButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _autoPlayNext = !_autoPlayNext;
          SharedPreferences.getInstance().then((prefs) {
            prefs.setBool('autoPlayNext', _autoPlayNext);
          });
          if (_autoPlayNext)
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'ఈ గేయం తరువాత ఆటోమేటిక్ గా తరువాతి గేయం ప్రారంభం అయ్యిది',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            );
          else
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        margin: const EdgeInsets.only(right: 10),
        decoration: BoxDecoration(
            border: Border.all(
                width: 1, color: _autoPlayNext ? Colors.blue : Colors.grey),
            borderRadius: BorderRadius.circular(5),
            color: _autoPlayNext ? Colors.blue[50] : null),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "AUTO",
              style: TextStyle(
                color: _autoPlayNext ? Colors.blue : null,
              ),
            )
          ],
        ),
      ),
    );
  }

  backForwardButton() {
    return IconButton(
      icon: Icon(
        Icons.replay_10,
        size: 30,
      ),
      onPressed: () async {
        int secounds = _player.position.inSeconds;
        if (secounds < 10) {
          await _player.seek(Duration(seconds: 0));
        } else {
          await _player.seek(Duration(seconds: secounds - 10));
        }
      },
    );
  }

  frontForwardButton() {
    return IconButton(
        icon: Icon(Icons.forward_10_rounded, size: 30),
        onPressed: () async {
          int secounds = _player.position.inSeconds;
          if (_player.duration!.inSeconds - secounds < 10) {
            await _player
                .seek(Duration(seconds: _player.duration!.inSeconds - 1));
          } else {
            await _player.seek(Duration(seconds: secounds + 10));
          }
        });
  }

  StreamBuilder<DurationState> _progressBar() {
    return StreamBuilder<DurationState>(
      stream: _durationState,
      builder: (context, snapshot) {
        final durationState = snapshot.data;
        final progress = durationState?.progress ?? Duration.zero;
        final buffered = durationState?.buffered ?? Duration.zero;
        final total = durationState?.total ?? Duration.zero;
        return ProgressBar(
          baseBarColor: Colors.blue[50],
          thumbColor: Colors.blue,
          thumbGlowColor: Colors.blue[200],
          progressBarColor: Colors.blue,
          bufferedBarColor: Colors.blue[100],
          progress: progress,
          buffered: buffered,
          total: total,
          onSeek: (duration) {
            _player.seek(duration);
          },
          timeLabelLocation: _labelLocation,
          timeLabelType: _labelType,
        );
      },
    );
  }

  StreamBuilder<PlayerState> _playButton() {
    return StreamBuilder<PlayerState>(
      stream: _player.playerStateStream,
      builder: (context, snapshot) {
        final playerState = snapshot.data;
        final processingState = playerState?.processingState;
        final playing = playerState?.playing;
        if (processingState == ProcessingState.loading ||
            processingState == ProcessingState.buffering) {
          return Container(
            margin: const EdgeInsets.all(8.0),
            width: 32.0,
            height: 32.0,
            child: const CircularProgressIndicator(),
          );
        } else if (playing != true) {
          return IconButton(
            icon: const Icon(Icons.play_arrow),
            iconSize: 32.0,
            onPressed: _player.play,
          );
        } else if (processingState != ProcessingState.completed) {
          return IconButton(
            icon: const Icon(Icons.pause),
            iconSize: 32.0,
            onPressed: _player.pause,
          );
        } else {
          return IconButton(
            icon: const Icon(Icons.replay),
            iconSize: 32.0,
            onPressed: () => _player.seek(Duration.zero),
          );
        }
      },
    );
  }
}

class DurationState {
  final Duration? progress;
  final Duration? buffered;
  final Duration? total;

  const DurationState({
    this.progress,
    this.buffered,
    this.total,
  });
}
